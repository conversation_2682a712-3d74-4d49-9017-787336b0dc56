import os, sys, json, time
from pathlib import Path

# Ensure we can import the pyd
BIN = Path(r"C:\TANK\ocr_accelerator\bin")
os.add_dll_directory(str(BIN)) if hasattr(os, 'add_dll_directory') else None
sys.path.insert(0, str(BIN))

# Enable C++ debug dumps and set tessdata
os.environ.setdefault('OCR_DEBUG_DUMP', '1')
os.environ.setdefault('TESSDATA_PREFIX', str(BIN / 'tessdata'))

import numpy as np
import mss, mss.tools as mstools
import accelerator_sharedmem as a
import statistics as stats
import json as _json
import csv
import ctypes
from ctypes import wintypes as _wt

# Optional OpenCV for --image path
try:
    import cv2
    HAVE_CV2 = True
except Exception:
    HAVE_CV2 = False

# Lightweight RSS reader (no psutil) using PROCESS_MEMORY_COUNTERS
class _PMCOUNTERS(ctypes.Structure):
    _fields_ = [
        ("cb", _wt.DWORD), ("PageFaultCount", _wt.DWORD),
        ("PeakWorkingSetSize", ctypes.c_size_t), ("WorkingSetSize", ctypes.c_size_t),
        ("QuotaPeakPagedPoolUsage", ctypes.c_size_t), ("QuotaPagedPoolUsage", ctypes.c_size_t),
        ("QuotaPeakNonPagedPoolUsage", ctypes.c_size_t), ("QuotaNonPagedPoolUsage", ctypes.c_size_t),
        ("PagefileUsage", ctypes.c_size_t), ("PeakPagefileUsage", ctypes.c_size_t),
    ]

# Configure Psapi.GetProcessMemoryInfo signature explicitly
try:
    _psapi = ctypes.WinDLL('Psapi.dll')
    _GetProcessMemoryInfo = _psapi.GetProcessMemoryInfo
    _GetProcessMemoryInfo.restype = _wt.BOOL
    _GetProcessMemoryInfo.argtypes = [_wt.HANDLE, ctypes.POINTER(_PMCOUNTERS), _wt.DWORD]
except Exception:
    _GetProcessMemoryInfo = None

def _get_rss_mb():
    try:
        if _GetProcessMemoryInfo is None:
            return float('nan')
        pmc = _PMCOUNTERS()
        pmc.cb = ctypes.sizeof(_PMCOUNTERS)
        hproc = ctypes.windll.kernel32.GetCurrentProcess()
        ok = _GetProcessMemoryInfo(hproc, ctypes.byref(pmc), pmc.cb)
        if ok:
            return pmc.WorkingSetSize / (1024.0 * 1024.0)
    except Exception:
        pass
    return float('nan')

# Optional image override via --image=path and monitor selection
img_path = None
monitor_index = 1  # default primary
for arg in sys.argv[1:]:
    if arg.startswith('--image='):
        img_path = arg.split('=',1)[1]
    elif arg.startswith('--monitor='):
        monitor_index = int(arg.split('=',1)[1])

CONTROL = Path(r"C:\TANK\utils\control.json")
assert CONTROL.exists(), f"Missing ROI control file: {CONTROL}"
ctl = json.loads(CONTROL.read_text())
roi = ctl.get('roi') or ctl.get('initial_roi')
if roi:
    x, y, w, h = int(roi['x']), int(roi['y']), int(roi['width']), int(roi['height'])
else:
    coords = ctl.get('ROI_COORDINATES')
    assert coords and len(coords)==4, "ROI missing in control.json (expected keys: roi, initial_roi, or ROI_COORDINATES [x1,y1,x2,y2])"
    x1,y1,x2,y2 = map(int, coords)
    x,y,w,h = x1, y1, x2 - x1, y2 - y1

# Optional ROI override flags: --roi=x,y,w,h or --top or --center
roi_override = None
center_flag = False
for arg in sys.argv[1:]:
    if arg.startswith('--roi='):
        try:
            parts = [int(v) for v in arg.split('=',1)[1].split(',')]
            if len(parts) == 4:
                roi_override = tuple(parts)
        except Exception:
            pass
    elif arg == '--top':
        roi_override = (0, 0, w, h)
    elif arg == '--center':
        center_flag = True

# Resolve ROI overrides
if roi_override:
    x, y, w, h = roi_override

print(f"[ISO] Using ROI x={x} y={y} w={w} h={h} on monitor {monitor_index}")

# Acquire BGRA frame: either from --image file or live MSS capture
if img_path and HAVE_CV2:
    img = cv2.imread(img_path, cv2.IMREAD_UNCHANGED)
    if img is None:
        raise SystemExit(f"Could not read image: {img_path}")
    # Resize to ROI exactly (apples-to-apples with live)
    if img.shape[1] != w or img.shape[0] != h:
        img = cv2.resize(img, (w, h), interpolation=cv2.INTER_AREA)
    if img.ndim == 2:
        img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGRA)
    elif img.shape[2] == 3:
        img = cv2.cvtColor(img, cv2.COLOR_BGR2BGRA)
    raw = img
else:
    if img_path and not HAVE_CV2:
        print("[ISO] OpenCV not available in this venv; falling back to MSS live capture.")
    sct = mss.mss()
    mons = sct.monitors
    if not (1 <= monitor_index < len(mons)):
        monitor_index = 1

    # Center ROI if requested (test convenience)
    if center_flag:
        mon_w = mons[monitor_index]['width']
        mon_h = mons[monitor_index]['height']
        x = max(0, (mon_w - w) // 2)
        y = max(0, (mon_h - h) // 2)

    abs_left = mons[monitor_index]['left'] + x
    abs_top  = mons[monitor_index]['top'] + y
    monitor = {'left': abs_left, 'top': abs_top, 'width': w, 'height': h}
    # Save the grabbed region for inspection (absolute coordinates)
    raw = np.array(sct.grab(monitor))  # BGRA by MSS
if raw.ndim != 3 or raw.shape[2] not in (3, 4):
    raise RuntimeError(f"Unexpected frame shape: {raw.shape}")
if raw.shape[2] == 3:
    alpha = np.full((raw.shape[0], raw.shape[1], 1), 255, dtype=raw.dtype)
    raw = np.concatenate([raw, alpha], axis=2)
if not raw.flags['C_CONTIGUOUS']:
    raw = np.ascontiguousarray(raw)


# Dump the raw MSS grab region for verification
try:
    from PIL import Image
    img_dbg = Image.fromarray(raw[..., :3][:, :, ::-1])  # convert BGRA/BGR to RGB approx
    img_dbg.save(r"C:\TANK\_iso_mss_abs.png")
except Exception:
    pass

name = 'isolated_ocr_buffer'
size = w * h * 4

# Create queues and engine
inq = a.MetadataQueue(16)
outq = a.ResultRingBuffer(32)
eng = a.OcrUnified()
# Prefer session-local first, then core will fall back to Global\
eng.initialize_shared_memory(name, size)
# Map control.json fields to core config keys (no hardcoding in core)
params = {
    'red_boost': float(ctl.get('ocr_red_boost', 1.0)),
    'green_boost': float(ctl.get('ocr_green_boost', 1.0)),
    'blue_boost': float(ctl.get('ocr_blue_boost', 1.0)),
    'adaptive_block_size': int(ctl.get('ocr_threshold_block_size', 11)),
    'adaptive_C': float(ctl.get('ocr_threshold_c', 2.0)),
    'unsharp_strength': float(ctl.get('ocr_unsharp_strength', 0.0)),
    'upscale_factor': int(ctl.get('ocr_upscale_factor', 1)),
    'force_black_text_on_white': bool(ctl.get('ocr_force_black_text_on_white', True)),
    'apply_text_mask_cleaning': bool(ctl.get('ocr_apply_text_mask_cleaning', False)),
    'text_mask_min_contour_area': int(ctl.get('ocr_text_mask_min_contour_area', 0)),
    'text_mask_min_width': int(ctl.get('ocr_text_mask_min_width', 0)),
    'text_mask_min_height': int(ctl.get('ocr_text_mask_min_height', 0)),
}
# DEBUG: Save params for comparison
import json
with open('C:/TANK/_debug_params_test.json', 'w') as f:
    json.dump(params, f, indent=2)
print(f"[ISO] Parameters written to C:/TANK/_debug_params_test.json")
eng.start(inq, outq, params)

# Continuous soak/stress test
print("[ISO] Starting continuous stress test... Press Ctrl+C to stop.")

timings = []
preproc_timings = []
ocr_timings = []
frames = 0
# Ring stats baseline
try:
    drops_baseline = inq.dropped_oldest_count()
except Exception:
    drops_baseline = 0

# Prepare MSS context for live capture if no image
sct_ctx = None
if not (img_path and HAVE_CV2):
    sct_ctx = mss.mss()
    mons = sct_ctx.monitors
    if not (1 <= monitor_index < len(mons)):
        monitor_index = 1
    abs_left = mons[monitor_index]['left'] + x
    abs_top  = mons[monitor_index]['top'] + y
    monitor = {'left': abs_left, 'top': abs_top, 'width': w, 'height': h}

try:
    while True:
        # Acquire frame (image reuse or live MSS)
        if img_path and HAVE_CV2:
            frame = raw  # reuse preloaded BGRA image
        else:
            frame = np.array(sct_ctx.grab(monitor), dtype=np.uint8)
            if frame.ndim == 3 and frame.shape[2] == 3:
                alpha = np.full((frame.shape[0], frame.shape[1], 1), 255, dtype=frame.dtype)
                frame = np.concatenate([frame, alpha], axis=2)
            if not frame.flags['C_CONTIGUOUS']:
                frame = np.ascontiguousarray(frame)

        # Push frame to shared memory and metadata to input queue
        eng.write_bytes(frame)
        t0 = time.perf_counter_ns()
        cid = f"iso_{t0}"
        inq.try_write(width=w, height=h, t0_ns=t0, correlation_id=cid)
        eng.notify_new_frame()

        # Drain output; accept first available result
        got = False
        for _ in range(200):  # up to ~200 * 1ms = 200ms wait per frame
            obj = outq.try_read()
            if obj is not None:
                # Print OCR text/confidence every frame
                txt = getattr(obj, 'text', '')
                conf = getattr(obj, 'confidence', float('nan'))
                print(f"[ISO] OCR: '{txt}' | conf={conf:.2f}")

                # Compute cycle time and log every 100 frames
                if all(hasattr(obj, k) for k in ('t0_ns','t1_ns','t2_ns','t3_ns','t4_ns')):
                    total_ms = (obj.t4_ns - obj.t0_ns) / 1e6
                    prep_ms = (obj.t2_ns - obj.t1_ns) / 1e6
                    ocr_ms = (obj.t3_ns - obj.t2_ns) / 1e6
                    timings.append(total_ms)
                    preproc_timings.append(prep_ms)
                    ocr_timings.append(ocr_ms)
                got = True
                frames += 1
                break
            time.sleep(0.001)

        if frames and frames % 100 == 0:
            avg_total = sum(timings)/len(timings) if timings else float('nan')
            avg_prep = sum(preproc_timings)/len(preproc_timings) if preproc_timings else float('nan')
            avg_ocr = sum(ocr_timings)/len(ocr_timings) if ocr_timings else float('nan')
            rss = _get_rss_mb()
            last_conf = conf if 'conf' in locals() else float('nan')
            # Ring stats
            try:
                drops = inq.dropped_oldest_count()
                util = inq.size()
                cap = inq.capacity()
            except Exception:
                drops, util, cap = (0, 0, 0)
            # Print rolling stats with RSS, timings, ring metrics
            print(f"[ISO] {frames} | Total: {avg_total:.2f} ms | Prep: {avg_prep:.2f} ms | OCR: {avg_ocr:.2f} ms | RSS: {rss:.1f} MB | Last conf: {last_conf:.2f} | InQ util: {util}/{cap} | Drops: {drops - drops_baseline}")
            # Append to CSV log
            try:
                with open(r"C:\\TANK\\_iso_memleak_log.csv", 'a', newline='') as f:
                    csv.writer(f).writerow([frames, f"{avg_total:.2f}", f"{rss:.1f}"])
            except Exception:
                pass
            timings.clear(); preproc_timings.clear(); ocr_timings.clear()

except KeyboardInterrupt:
    print("\n[ISO] Stress test stopped.")
finally:
    eng.stop()
    if sct_ctx:
        try:
            sct_ctx.close()
        except Exception:
            pass

print("[ISO] Check these files (overwritten each frame):")
print(r"  C:\\TANK\\_debug_mss_bgra.png")
print(r"  C:\\TANK\\_debug_processed.png")
print(r"  C:\\TANK\\_debug_ocr_text.txt")

