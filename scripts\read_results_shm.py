import os
import time
from utils.shm_results import LatestResultReader

if __name__ == '__main__':
    name = os.environ.get('OCR_RESULTS_SHM_NAME', 'Local\\testrade_ocr_results')
    rdr = LatestResultReader(name)
    print(f"Reading latest OCR result from {name} (Ctrl+C to exit)")
    try:
        while True:
            msg = rdr.try_read()
            if msg:
                print(msg)
            time.sleep(0.05)
    except KeyboardInterrupt:
        pass

