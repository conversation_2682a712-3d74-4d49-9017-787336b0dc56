import os
import sys
import time
import json
from pathlib import Path

import numpy as np

try:
    import cv2
    HAVE_CV2 = True
except Exception:
    HAVE_CV2 = False

try:
    import mss
    HAVE_MSS = True
except Exception:
    HAVE_MSS = False

# Ensure the PYD is importable (fallback to bin path if not on sys.path)
try:
    import accelerator_sharedmem as a
except ModuleNotFoundError:
    bin_path = r"C:\\TANK\\ocr_accelerator\\bin"
    if bin_path not in sys.path:
        sys.path.insert(0, bin_path)
    import accelerator_sharedmem as a  # may still fail if not built yet

"""
Simple cold-path verification harness.
- Starts OcrUnified with hot and cold rings
- Captures a few frames (MSS or --image), pushes into engine
- Reads a few cold TelemetryBlob entries
- Reconstructs images and dumps them to disk
- Prints metadata to console

Usage examples:
  python scripts/verify_telemetry_blob.py --monitor=1 --center --count=5
  python scripts/verify_telemetry_blob.py --image=C:\path\to\sample.png --count=3

Environment:
  PATH should include C:\TANK\ocr_accelerator\bin
  TESSDATA_PREFIX optional; core will fall back to bin\tessdata
"""

# Args
monitor_index = 1
center_flag = False
img_path = None
count = 3
for arg in sys.argv[1:]:
    if arg.startswith('--monitor='):
        try: monitor_index = int(arg.split('=',1)[1])
        except: pass
    elif arg == '--center':
        center_flag = True
    elif arg.startswith('--image='):
        img_path = arg.split('=',1)[1]
    elif arg.startswith('--count='):
        try: count = int(arg.split('=',1)[1])
        except: pass

# Control config
# Use same control file path and mapping as soak
CONTROL = Path(r"C:\TANK\utils\control.json")
assert CONTROL.exists(), f"Missing control file: {CONTROL}"
ctl = json.loads(CONTROL.read_text())
# ROI from legacy ROI_COORDINATES [x1,y1,x2,y2]
coords = ctl.get('ROI_COORDINATES')
assert coords and len(coords)==4, "ROI_COORDINATES missing in control.json"
x1,y1,x2,y2 = map(int, coords)
x,y,w,h = x1, y1, x2 - x1, y2 - y1

# Output dir
OUTDIR = Path(r"C:\TANK\_telemetry_test")
OUTDIR.mkdir(parents=True, exist_ok=True)

# Create rings and engine
inq = a.MetadataQueue(2)
hot = a.ResultRingBuffer(32)
cold = a.TelemetryRingBuffer(1024)
eng = a.OcrUnified()

# Initialize shared memory region (size = w*h*4 bytes)
shm_name = "ocr_shm_verify"
eng.initialize_shared_memory(shm_name, int(w*h*4))

# Start engine with both hot and cold outputs
# Map control.json fields to core config keys (same mapping as soak)
params = {
    'red_boost': float(ctl.get('ocr_red_boost', 1.0)),
    'green_boost': float(ctl.get('ocr_green_boost', 1.0)),
    'blue_boost': float(ctl.get('ocr_blue_boost', 1.0)),
    'adaptive_block_size': int(ctl.get('ocr_threshold_block_size', 25)),
    'adaptive_C': float(ctl.get('ocr_threshold_c', -6.0)),
    'upscale_factor': int(ctl.get('ocr_upscale_factor', 3)),
    'unsharp_strength': float(ctl.get('ocr_unsharp_strength', 1.7)),
    'force_black_text_on_white': bool(ctl.get('ocr_force_black_text_on_white', True)),
    'apply_text_mask_cleaning': bool(ctl.get('ocr_apply_text_mask_cleaning', False)),
    'text_mask_min_contour_area': int(ctl.get('ocr_text_mask_min_contour_area', 0)),
    'text_mask_min_width': int(ctl.get('ocr_text_mask_min_width', 0)),
    'text_mask_min_height': int(ctl.get('ocr_text_mask_min_height', 0)),
}
eng.start(inq, hot, cold, params)

# Prepare capture helpers
sct = None
monitor = None
if img_path and HAVE_CV2:
    img = cv2.imread(img_path, cv2.IMREAD_UNCHANGED)
    assert img is not None, f"Could not read image: {img_path}"
    if img.ndim == 2:
        img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGRA)
    elif img.shape[2] == 3:
        img = cv2.cvtColor(img, cv2.COLOR_BGR2BGRA)
    if img.shape[1] != w or img.shape[0] != h:
        img = cv2.resize(img, (w, h), interpolation=cv2.INTER_AREA)
    sample_frame = img
else:
    if not HAVE_MSS:
        raise SystemExit("MSS not available and no --image provided")
    sct = mss.mss()
    mons = sct.monitors
    if not (1 <= monitor_index < len(mons)):
        monitor_index = 1
    # Always center ROI within chosen monitor when --center or TANK TEST mode
    center_flag = True if os.environ.get('TANK_TEST_CENTER','1') in ('1','true','True') else center_flag
    mon_w = mons[monitor_index]['width']
    mon_h = mons[monitor_index]['height']
    if center_flag:
        x = max(0, (mon_w - w) // 2)
        y = max(0, (mon_h - h) // 2)
    abs_left = mons[monitor_index]['left'] + x
    abs_top  = mons[monitor_index]['top'] + y
    monitor = {'left': abs_left, 'top': abs_top, 'width': w, 'height': h}

print(f"[VERIFY] ROI x={x} y={y} w={w} h={h} monitor={monitor_index}")

# Push frames and collect blobs
collected = 0
start_time = time.time()
try:
    while collected < count:
        # Acquire frame
        if img_path and HAVE_CV2:
            frame = sample_frame
        else:
            grab = np.array(sct.grab(monitor), dtype=np.uint8)
            if grab.ndim == 3 and grab.shape[2] == 3:
                alpha = np.full((grab.shape[0], grab.shape[1], 1), 255, dtype=np.uint8)
                frame = np.concatenate([grab, alpha], axis=2)
            else:
                frame = grab
        if not frame.flags['C_CONTIGUOUS']:
            frame = np.ascontiguousarray(frame)

        # Write to SHM and signal metadata
        eng.write_bytes(frame)
        t0 = time.perf_counter_ns()
        cid = f"verify_{t0}"
        inq.try_write(width=w, height=h, t0_ns=t0, correlation_id=cid)
        eng.notify_new_frame()

        # Read cold blob (with small wait loop)
        got = False
        for _ in range(300):  # up to ~300 ms total
            blob = cold.try_read()
            if blob is not None:
                collected += 1
                print(f"[VERIFY] Blob {collected}: text='{blob.get('text','')[:60]}' conf={blob.get('confidence',0.0):.2f}")
                # Reconstruct images from bytes
                W, H = blob['width'], blob['height']
                PW, PH = blob['proc_width'], blob['proc_height']
                raw_bytes = blob['raw_bgra']
                proc_bytes = blob['processed_bin']
                raw = np.frombuffer(raw_bytes, dtype=np.uint8).reshape((H, W, 4))
                proc = np.frombuffer(proc_bytes, dtype=np.uint8).reshape((PH, PW))
                # Save
                if HAVE_CV2:
                    raw_bgr = cv2.cvtColor(raw, cv2.COLOR_BGRA2BGR)
                    cv2.imwrite(str(OUTDIR / f"raw_{collected}.png"), raw_bgr)
                    cv2.imwrite(str(OUTDIR / f"processed_{collected}.png"), proc)
                else:
                    # Minimal fallback: save npy files
                    np.save(OUTDIR / f"raw_{collected}.npy", raw)
                    np.save(OUTDIR / f"processed_{collected}.npy", proc)
                # Metadata dump
                print(f"[VERIFY] t0..t4: {blob.get('t0_ns')}, {blob.get('t1_ns')}, {blob.get('t2_ns')}, {blob.get('t3_ns')}, {blob.get('t4_ns')}")
                got = True
                break
            time.sleep(0.001)
        if not got:
            print("[VERIFY] Warning: No telemetry blob received for this frame (cold consumer lag?)")

    dur = time.time() - start_time
    print(f"[VERIFY] Completed: {collected} blobs in {dur:.2f}s. Output: {OUTDIR}")
finally:
    try:
        eng.stop()
    except Exception:
        pass
    if sct:
        try: sct.close()
        except Exception: pass

