# Fix start_tank_sealed.bat  
with open('start_tank_sealed.bat', 'r') as f: lines = f.readlines()  
for i, line in enumerate(lines):  
    if 'set TESTRADE_MODE=TANK_SEALED' in line:  
        lines.insert(i+1, '\nREM OCR Performance Settings\nset PATH=C:\\TANK\\ocr_accelerator\\bin;%C:\Python311\Scripts\;C:\Python311\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;C:\Program Files\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin%\nset TESSDATA_PREFIX=C:\\TANK\\ocr_accelerator\\bin\\tessdata\nset OCR_PIPE_DISABLED=1\nset OCR_DEBUG_DUMP=0\nset OCR_DEBUG_DUMP_EVERY=0\nset TANK_TEST_CENTER=1\n\n')  
        break  
with open('start_tank_sealed.bat', 'w') as f: f.writelines(lines)  
print('Fixed')  
