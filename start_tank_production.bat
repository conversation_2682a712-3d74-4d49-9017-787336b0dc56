@echo off  
REM TESTRADE Production Mode - Optimized for performance  
  
title TESTRADE Production Mode  
cd /d C:\TANK  
  
REM Activate virtual environment  
call .venv\Scripts\activate.bat  
  
REM Set production environment variables  
set TESTRADE_MODE=TANK_SEALED  
set PYTHONHTTPSVERIFY=0  
  
REM OCR Performance Settings - Production Ready  
set PATH=C:\TANK\ocr_accelerator\bin;%C:\Python311\Scripts\;C:\Python311\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;C:\Program Files\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin% & set TESSDATA_PREFIX=C:\TANK\ocr_accelerator\bin\tessdata  
set OCR_PIPE_DISABLED=1  
set OCR_DEBUG_DUMP=0  
set OCR_DEBUG_DUMP_EVERY=0  
set TANK_TEST_CENTER=0  
  
REM Update control.json for production mode  
python -c "import json; c=json.load(open('utils/control.json')); c.update({'BABYSITTER_ENABLED':False,'ENABLE_IPC_DATA_DUMP':False,'ENABLE_EMERGENCY_GUI':False,'enable_intellisense_logging':False,'disable_broker':False,'disable_price_fetching_service':False,'development_mode':False}); json.dump(c, open('utils/control.json','w'), indent=4)"  
  
echo Starting TESTRADE in Production Mode...  
echo OCR: Debug dumps DISABLED for maximum performance  
echo OCR: Running at full speed with 0.93 confidence  
  
python main.py  
  
pause 
