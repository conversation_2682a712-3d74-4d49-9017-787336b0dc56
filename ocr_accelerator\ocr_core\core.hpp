#pragma once
#include <cstdint>
#include <atomic>
#include <thread>
#include <memory>
#include <string>
#include <vector>
#include <functional>
#include <windows.h>

#include <opencv2/opencv.hpp>
#include <tesseract/baseapi.h>

namespace ocr_core {

struct Config {
    // Channel boost (BGRA order)
    double blue_boost = 1.0;
    double green_boost = 1.0;
    double red_boost = 1.0;

    // Threshold (Adaptive Gaussian)
    int adaptive_block_size = 25; // threshold_block_size in control.json
    double adaptive_C = -6.0;     // threshold_c in control.json

    // Upscale + sharpening
    int upscale_factor = 1;             // e.g., 3
    double unsharp_strength = 0.0;      // e.g., 1.7 (applied after grayscale)

    // Polarity
    bool force_black_text_on_white = true; // normalize to black text on white

    // Cleaning
    bool apply_text_mask_cleaning = false;
    int text_mask_min_contour_area = 0;
    int text_mask_min_width = 0;
    int text_mask_min_height = 0;

    // Symbol enhancement
    bool enhance_small_symbols = false;
    int symbol_max_height_upscaled = 0; // e.g., 30 (10 * upscale)
    double period_comma_aspect_min = 0.2;
    double period_comma_aspect_max = 1.2;
    int period_comma_draw_radius_upscaled = 5; // e.g., 15 (5*upscale)
    double hyphen_like_min_aspect_ratio_upscaled = 3.0;
    int hyphen_like_draw_min_height_upscaled = 3; // e.g., 9 (3*upscale)

    // Legacy/optional knobs retained for parity (unused unless configured)
    double clahe_clip_limit = 0.0; // disabled when 0
    int clahe_tile_size = 8;
    int morph_kernel_size = 0;     // disabled when 0
    int bilateral_d = 0;           // disabled when 0
    double bilateral_sigma_color = 0.0;
    double bilateral_sigma_space = 0.0;
    int denoise_h = 0;
};

struct MetadataPacket {
    int width;
    int height;
    int64_t t0_mss_ns;
    char correlation_id[64];
};

// Hot path result (trading)
struct OcrResultPackage {
    char text[512];
    char correlation_id[64];
    int64_t t0_ns;
    int64_t t1_ns;
    int64_t t2_ns;
    int64_t t3_ns;
    int64_t t4_ns;
    float confidence;
};

// Cold path telemetry blob (in-process version)
struct TelemetryBlob {
    // Image sizes
    int width;
    int height;
    int proc_width;
    int proc_height;
    // Raw BGRA and processed binary snapshots
    std::vector<unsigned char> raw_bgra;
    std::vector<unsigned char> processed_bin;
    // Copy of hot metadata
    OcrResultPackage trading;
};

// Single-producer single-consumer lock-free ring buffer
// Capacity must be a power of two for modulo via &
template <typename T, size_t Capacity = 1024>
class SPSCQueue {
public:
    SPSCQueue() = default;

    // Standard try_write: fails if full
    bool try_write(const T &item) {
        const auto head = _head.load(std::memory_order_relaxed);
        const auto next = (head + 1) & (Capacity - 1);
        if (next == _tail.load(std::memory_order_acquire)) {
            return false; // full
        }
        _ring[head] = item;
        _head.store(next, std::memory_order_release);
        return true;
    }

    // Leaky try_write: drops oldest on full to accept newest (for size-2 MSS queue)
    bool try_write_drop_oldest(const T &item) {
        auto head = _head.load(std::memory_order_relaxed);
        const auto next = (head + 1) & (Capacity - 1);
        auto tail = _tail.load(std::memory_order_acquire);
        if (next == tail) {
            _tail.store((tail + 1) & (Capacity - 1), std::memory_order_release);
            _drop_count.fetch_add(1, std::memory_order_relaxed);
        }
        _ring[head] = item;
        _head.store(next, std::memory_order_release);
        return true;
    }

    bool try_read(T &item) {
        const auto tail = _tail.load(std::memory_order_relaxed);
        if (tail == _head.load(std::memory_order_acquire)) {
            return false; // empty
        }
        item = _ring[tail];
        _tail.store((tail + 1) & (Capacity - 1), std::memory_order_release);
        return true;
    }

    // Introspection for health metrics
    size_t size() const {
        const auto head = _head.load(std::memory_order_acquire);
        const auto tail = _tail.load(std::memory_order_acquire);
        return (head - tail) & (Capacity - 1);
    }
    constexpr size_t capacity() const { return Capacity - 1; }
    size_t dropped_oldest_count() const { return _drop_count.load(std::memory_order_relaxed); }

private:
    alignas(64) T _ring[Capacity]{};
    alignas(64) std::atomic<size_t> _head{0};
    alignas(64) std::atomic<size_t> _tail{0};
    alignas(64) std::atomic<size_t> _drop_count{0};
};

class OcrUnified {
public:
    using LogFn = std::function<void(const char*)>;

    OcrUnified();
    ~OcrUnified();

    void set_logger(LogFn fn) { _logger = std::move(fn); }

    void initialize_shared_memory(const std::string &name, size_t size);
    void cleanup_shared_memory();

    // Start with hot (and optional cold) outputs
    void start(SPSCQueue<MetadataPacket, 2> &input_q,
               SPSCQueue<OcrResultPackage> &hot_out,
               const Config &cfg);

    void start(SPSCQueue<MetadataPacket, 2> &input_q,
               SPSCQueue<OcrResultPackage> &hot_out,
               SPSCQueue<TelemetryBlob, 1024> &cold_out,
               const Config &cfg);

    void stop();

    // Producer hint to wake worker immediately
    void notify_new_frame();

    // Write raw bytes into shared memory (BGRA contiguous). Bounds-checked.
    void write_bytes(const void* data, size_t size);

    // Expose preprocess for smoke tests and validation (read-only view)
    const cv::Mat& preprocess_bgra_to_ocr(const cv::Mat &input_bgra);

private:
    void processFrame(const MetadataPacket &metadata);
    void log(const char *msg);

    // Preallocated mats for in-place pipeline
    cv::Mat _mat_boosted, _mat_up, _mat_gray, _mat_sharp, _mat_thresh, _mat_mask;

    // Worker state
    std::atomic<bool> _running{false};
    std::thread _worker;
    std::condition_variable _cv;
    std::mutex _mtx;
    std::atomic<bool> _notified{false};

    // Tesseract
    std::unique_ptr<tesseract::TessBaseAPI> _tess;

    // Config
    Config _cfg{};

    // Shared memory
    HANDLE _shm_handle{nullptr};
    unsigned char *_shm_ptr{nullptr};
    size_t _shm_size{0};
    std::string _shm_name;

    // Output rings (non-owning)
    SPSCQueue<OcrResultPackage>* _hot_out{nullptr};
    SPSCQueue<TelemetryBlob, 1024>* _cold_out{nullptr};

    // Logger
    LogFn _logger{};
};

} // namespace ocr_core

