import sys  
  
with open('modules/ocr/ocr_service.py', 'r') as f:  
    lines = f.readlines()  
  
for i, line in enumerate(lines):  
    if 'np.copyto(target_view, frame_bgra)' in line:  
        print(f'Found np.copyto at line {i+1}')  
        lines.insert(i+1, '                    # CRITICAL FIX: write_bytes call\n')  
        lines.insert(i+2, '                    self._cpp_factory.write_bytes(frame_bgra)\n')  
        break  
  
with open('modules/ocr/ocr_service.py', 'w') as f:  
    f.writelines(lines)  
  
print('Fixed') 
