#include "core.hpp"
#include <chrono>
#include <fstream>
#include <filesystem>
#include <cstring>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <immintrin.h>

using namespace std::chrono;

namespace ocr_core {

static void default_log(const char *msg) {
    if (!msg) return;
    static int s_inited = 0; static bool s_verbose = false;
    if (!s_inited) {
        s_inited = 1;
        if (const char *v = std::getenv("OCR_VERBOSE")) {
            s_verbose = (v[0]=='1' || v[0]=='T' || v[0]=='t' || v[0]=='Y' || v[0]=='y');
        }
    }
    if (!s_verbose) {
        // Only allow essential logs when not verbose
        // Pass through HOT lines and error/warning
        if (std::strstr(msg, "HOT[") == nullptr &&
            std::strstr(msg, "ERROR") == nullptr &&
            std::strstr(msg, "WARNING") == nullptr) {
            return;
        }
    }
    printf("%s\n", msg);
}

// Atomic counter for debug dump throttling
static std::atomic<uint64_t> g_dump_counter{0};

static int get_dump_every() {
    const char *ev = std::getenv("OCR_DEBUG_DUMP_EVERY");
    if (!ev) return 1;
    int n = std::atoi(ev);
    return (n <= 0 ? 1 : n);
}

static void safe_write_png(const char *path, const cv::Mat &img) {
    // Write as raw binary file with header since OpenCV codecs are broken
    try {
        std::string raw_path = std::string(path) + ".raw";
        FILE* fp = fopen(raw_path.c_str(), "wb");
        if (!fp) {
            std::cout << "[OCR-CORE-DIAGNOSTIC] Cannot open file: " << raw_path << std::endl;
            return;
        }
        
        // Write simple header: width, height, channels, type
        int header[4] = {img.cols, img.rows, img.channels(), img.type()};
        fwrite(header, sizeof(int), 4, fp);
        
        // Write raw pixel data
        fwrite(img.data, 1, img.total() * img.elemSize(), fp);
        fclose(fp);
        
        std::cout << "[OCR-CORE-DIAGNOSTIC] Wrote raw image: " << raw_path 
                  << " (" << img.cols << "x" << img.rows << "x" << img.channels() << ")" << std::endl;
    } catch (...) {
        std::cout << "[OCR-CORE-DIAGNOSTIC] Failed to write raw image" << std::endl;
    }
}

static void safe_write_text(const char *path, const std::string &text) {
    try {
        std::string tmp = std::string(path) + ".tmp";
        std::ofstream f(tmp, std::ios::trunc);
        if (f.is_open()) { f << text; f.close(); }
        MoveFileExA(tmp.c_str(), path, MOVEFILE_REPLACE_EXISTING | MOVEFILE_COPY_ALLOWED);
    } catch (...) {}
}

OcrUnified::OcrUnified() { _logger = default_log; }
OcrUnified::~OcrUnified() { stop(); cleanup_shared_memory(); }

void OcrUnified::log(const char *msg) { if (_logger) _logger(msg); }

void OcrUnified::initialize_shared_memory(const std::string &name, size_t size) {
    _shm_size = size;

    // Try exact name first (session-local). If that fails, try Global\\ prefix.
    auto try_open = [&](const std::string& nm) -> HANDLE {
        return OpenFileMappingA(FILE_MAP_ALL_ACCESS, FALSE, nm.c_str());
    };
    auto try_create = [&](const std::string& nm) -> HANDLE {
        return CreateFileMappingA(INVALID_HANDLE_VALUE, NULL, PAGE_READWRITE, 0, (DWORD)size, nm.c_str());
    };

    std::string nm1 = name;
    std::string nm2 = std::string("Global\\") + name;

    _shm_handle = try_open(nm1);
    if (_shm_handle) { _shm_name = nm1; log("[OCR-CORE] Opened existing shared memory"); }

    if (!_shm_handle) {
        _shm_handle = try_open(nm2);
        if (_shm_handle) { _shm_name = nm2; log("[OCR-CORE] Opened existing shared memory (Global)"); }
    }

    if (!_shm_handle) {
        _shm_handle = try_create(nm1);
        if (_shm_handle) { _shm_name = nm1; log("[OCR-CORE] Created new shared memory"); }
    }
    if (!_shm_handle) {
        _shm_handle = try_create(nm2);
        if (_shm_handle) { _shm_name = nm2; log("[OCR-CORE] Created new shared memory (Global)"); }
    }

    if (_shm_handle) {
        // Map entire view (dwNumberOfBytesToMap = 0), which is the full section size
        _shm_ptr = (unsigned char *)MapViewOfFile(_shm_handle, FILE_MAP_ALL_ACCESS, 0, 0, 0);
        if (_shm_ptr) {
            char buf[224]; snprintf(buf, sizeof(buf), "[OCR-CORE] Shared memory mapped at: %p (name='%s', size=%zu)", _shm_ptr, _shm_name.c_str(), _shm_size); log(buf);
        } else {
            char buf[200]; snprintf(buf, sizeof(buf), "[OCR-CORE] MapViewOfFile failed (GetLastError=%lu) for name='%s'", GetLastError(), _shm_name.c_str()); log(buf);
        }
    } else {
        log("[OCR-CORE] Failed to create or open shared memory");
    }
}

void OcrUnified::cleanup_shared_memory() {
    if (_shm_ptr) { UnmapViewOfFile(_shm_ptr); _shm_ptr = nullptr; }
    if (_shm_handle) { CloseHandle(_shm_handle); _shm_handle = nullptr; }
}

// In-place, stateful preprocess per secret sauce order
const cv::Mat& OcrUnified::preprocess_bgra_to_ocr(const cv::Mat &input_bgra) {
    // 1) Channel boost (BGRA)
    {
        std::vector<cv::Mat> ch; cv::split(input_bgra, ch);
        ch[0].convertTo(ch[0], -1, _cfg.blue_boost);
        ch[1].convertTo(ch[1], -1, _cfg.green_boost);
        ch[2].convertTo(ch[2], -1, _cfg.red_boost);
        cv::merge(ch, _mat_boosted);
    }

    // 2) Upscale (INTER_CUBIC)
    if (_cfg.upscale_factor > 1) {
        cv::resize(_mat_boosted, _mat_up, cv::Size(), _cfg.upscale_factor, _cfg.upscale_factor, cv::INTER_CUBIC);
    } else {
        _mat_up = _mat_boosted;
    }

    // 3) Gray
    cv::cvtColor(_mat_up, _mat_gray, cv::COLOR_BGRA2GRAY);

    // 4) Unsharp
    if (_cfg.unsharp_strength > 0.0) {
        cv::GaussianBlur(_mat_gray, _mat_sharp, cv::Size(3,3), 0);
        cv::addWeighted(_mat_gray, 1.0 + _cfg.unsharp_strength, _mat_sharp, -_cfg.unsharp_strength, 0, _mat_sharp);
    } else {
        _mat_sharp = _mat_gray;
    }

    // 5) Adaptive Threshold (Gaussian, THRESH_BINARY)
    cv::adaptiveThreshold(_mat_sharp, _mat_thresh, 255, cv::ADAPTIVE_THRESH_GAUSSIAN_C, cv::THRESH_BINARY, _cfg.adaptive_block_size, _cfg.adaptive_C);

    // 6) Polarity normalization
    if (_cfg.force_black_text_on_white) {
        double meanVal = cv::mean(_mat_thresh)[0];
        if (meanVal < 127.0) cv::bitwise_not(_mat_thresh, _mat_thresh);
    }

    // 7) Clean / Enhance (optional)
    if (_cfg.apply_text_mask_cleaning) {
        std::vector<std::vector<cv::Point>> contours; std::vector<cv::Vec4i> hierarchy;
        cv::findContours(_mat_thresh.clone(), contours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
        _mat_mask.create(_mat_thresh.size(), CV_8UC1); _mat_mask.setTo(0);
        for (auto &cnt : contours) {
            double area = cv::contourArea(cnt);
            cv::Rect bb = cv::boundingRect(cnt);
            if (area >= _cfg.text_mask_min_contour_area && bb.width >= _cfg.text_mask_min_width && bb.height >= _cfg.text_mask_min_height) {
                cv::drawContours(_mat_mask, std::vector<std::vector<cv::Point>>{cnt}, -1, cv::Scalar(255), cv::FILLED);
            }
        }
        cv::bitwise_and(_mat_thresh, _mat_mask, _mat_thresh);
    }

    // Symbol enhancement (optional)
    if (_cfg.enhance_small_symbols && _cfg.upscale_factor > 1) {
        int max_h = _cfg.symbol_max_height_upscaled > 0 ? _cfg.symbol_max_height_upscaled : 10 * _cfg.upscale_factor;
        double ar_min = _cfg.period_comma_aspect_min, ar_max = _cfg.period_comma_aspect_max;
        int r = _cfg.period_comma_draw_radius_upscaled > 0 ? _cfg.period_comma_draw_radius_upscaled : 5 * _cfg.upscale_factor;
        double hy_ar = _cfg.hyphen_like_min_aspect_ratio_upscaled;
        int hy_h = _cfg.hyphen_like_draw_min_height_upscaled > 0 ? _cfg.hyphen_like_draw_min_height_upscaled : 3 * _cfg.upscale_factor;

        std::vector<std::vector<cv::Point>> contours; std::vector<cv::Vec4i> hierarchy;
        cv::findContours(_mat_thresh.clone(), contours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
        for (auto &cnt : contours) {
            cv::Rect bb = cv::boundingRect(cnt);
            if (bb.height <= max_h) {
                double ar = (double)bb.width / std::max(1, bb.height);
                if (ar >= ar_min && ar <= ar_max) {
                    cv::Point center(bb.x + bb.width/2, bb.y + bb.height/2);
                    cv::circle(_mat_thresh, center, r, cv::Scalar(255), cv::FILLED);
                } else if (ar >= hy_ar && bb.height >= hy_h) {
                    cv::rectangle(_mat_thresh, bb, cv::Scalar(255), cv::FILLED);
                }
            }
        }
    }

    return _mat_thresh;
}

void OcrUnified::start(SPSCQueue<MetadataPacket,2> &input_q,
                       SPSCQueue<OcrResultPackage> &hot_out,
                       const Config &cfg) {
    _hot_out = &hot_out;

    if (_running.exchange(true)) return;

    _cfg = cfg;
    
    // DIAGNOSTIC: Dump all OpenCV parameters at startup
    log("[OCR-CORE-DIAGNOSTIC] ===== PARAMETER DUMP START =====");
    char param_buf[512];
    snprintf(param_buf, sizeof(param_buf), "[PARAMS] Boosts: red=%.2f green=%.2f blue=%.2f", 
             _cfg.red_boost, _cfg.green_boost, _cfg.blue_boost);
    log(param_buf);
    snprintf(param_buf, sizeof(param_buf), "[PARAMS] Upscale: factor=%d, unsharp_strength=%.2f", 
             _cfg.upscale_factor, _cfg.unsharp_strength);
    log(param_buf);
    snprintf(param_buf, sizeof(param_buf), "[PARAMS] Adaptive: block_size=%d, C=%.2f", 
             _cfg.adaptive_block_size, _cfg.adaptive_C);
    log(param_buf);
    snprintf(param_buf, sizeof(param_buf), "[PARAMS] Cleaning: apply=%d, min_area=%d, min_w=%d, min_h=%d", 
             _cfg.apply_text_mask_cleaning, _cfg.text_mask_min_contour_area, 
             _cfg.text_mask_min_width, _cfg.text_mask_min_height);
    log(param_buf);
    snprintf(param_buf, sizeof(param_buf), "[PARAMS] Symbols: enhance=%d, max_h=%d, period_r=%d", 
             _cfg.enhance_small_symbols, _cfg.symbol_max_height_upscaled, 
             _cfg.period_comma_draw_radius_upscaled);
    log(param_buf);
    snprintf(param_buf, sizeof(param_buf), "[PARAMS] Polarity: force_black_on_white=%d", 
             _cfg.force_black_text_on_white);
    log(param_buf);
    log("[OCR-CORE-DIAGNOSTIC] ===== PARAMETER DUMP END =====");

    _tess = std::make_unique<tesseract::TessBaseAPI>();

    // Resolve tessdata robustly: prefer env; else look relative to process cwd and bin
    std::string tessdata_path;
    if (const char *env = std::getenv("TESSDATA_PREFIX"); env && std::filesystem::exists(env)) {
        tessdata_path = env;
        log("[OCR-CORE-DIAGNOSTIC] Using TESSDATA_PREFIX from environment");
    } else {
        // Try C:\TANK\ocr_accelerator\bin\tessdata if present; else local .\tessdata
        tessdata_path = "C:/TANK/ocr_accelerator/bin/tessdata";
        if (!std::filesystem::exists(tessdata_path)) {
            tessdata_path = ".\\tessdata";
            log("[OCR-CORE-DIAGNOSTIC] Fallback to local ./tessdata");
        } else {
            log("[OCR-CORE-DIAGNOSTIC] Using hardcoded C:/TANK/ocr_accelerator/bin/tessdata");
        }
    }
    
    // DIAGNOSTIC: Log the exact tessdata path being used
    char tess_buf[512];
    snprintf(tess_buf, sizeof(tess_buf), "[OCR-CORE-DIAGNOSTIC] Attempting Tesseract init with path: '%s'", tessdata_path.c_str());
    log(tess_buf);
    
    // Check if eng.traineddata exists
    std::string eng_file = tessdata_path + "/eng.traineddata";
    if (std::filesystem::exists(eng_file)) {
        snprintf(tess_buf, sizeof(tess_buf), "[OCR-CORE-DIAGNOSTIC] Found eng.traineddata at: %s (size=%lld bytes)", 
                 eng_file.c_str(), (long long)std::filesystem::file_size(eng_file));
        log(tess_buf);
    } else {
        snprintf(tess_buf, sizeof(tess_buf), "[OCR-CORE-DIAGNOSTIC] WARNING: eng.traineddata NOT FOUND at: %s", eng_file.c_str());
        log(tess_buf);
    }
    
    if (_tess->Init(tessdata_path.c_str(), "eng") != 0) {
        char buf[512]; snprintf(buf, sizeof(buf), "[OCR-CORE-DIAGNOSTIC] FAILED to initialize Tesseract (TESSDATA='%s')", tessdata_path.c_str());
        log(buf);
        _tess.reset();
    } else {
        log("[OCR-CORE-DIAGNOSTIC] Tesseract initialized SUCCESSFULLY");
        _tess->SetPageSegMode(tesseract::PSM_SINGLE_BLOCK); // PSM 6
        // OEM and performance flags per proven config
        _tess->SetVariable("tessedit_ocr_engine_mode", "2"); // LSTM only
        _tess->SetVariable("load_system_dawg", "false");
        _tess->SetVariable("load_freq_dawg", "false");
        _tess->SetVariable("tessedit_enable_doc_dict", "0");
        _tess->SetVariable("tessedit_enable_bigram_correction", "0");
        _tess->SetVariable("textord_heavy_nr", "0");
        _tess->SetVariable("debug_file", "NUL");
        snprintf(tess_buf, sizeof(tess_buf), "[OCR-CORE-DIAGNOSTIC] Tesseract fully configured with tessdata: %s", tessdata_path.c_str());
        log(tess_buf);
    }

    log("[OCR-CORE] Worker thread starting");
    _worker = std::thread([this, &input_q]() {
        std::unique_lock<std::mutex> lk(_mtx);
        while (_running.load()) {
            _cv.wait(lk, [this]{ return _notified.load() || !_running.load(); });
            _notified.store(false, std::memory_order_relaxed);
            lk.unlock();
            // Drain all available metadata packets
            MetadataPacket md{};
            while (input_q.try_read(md)) {

                processFrame(md);
            }
            lk.lock();
        }
    });
}

void OcrUnified::write_bytes(const void* data, size_t size) {
    if (!_shm_ptr || _shm_size == 0 || !data) return;
    size_t n = std::min<size_t>(size, _shm_size);
    std::memcpy(_shm_ptr, data, n);
}


void OcrUnified::stop() {
    if (!_running.exchange(false)) return;
    {
        std::lock_guard<std::mutex> g(_mtx);
        _notified.store(true, std::memory_order_relaxed);
    }
    _cv.notify_one();
    if (_worker.joinable()) _worker.join();
    log("[OCR-CORE] Worker thread stopped");
}

void OcrUnified::notify_new_frame() {
    {
        std::lock_guard<std::mutex> g(_mtx);
        _notified.store(true, std::memory_order_relaxed);
    }
    _cv.notify_one();
}

void OcrUnified::start(SPSCQueue<MetadataPacket,2> &input_q,
                       SPSCQueue<OcrResultPackage> &hot_out,
                       SPSCQueue<TelemetryBlob, 1024> &cold_out,
                       const Config &cfg) {
    _cold_out = &cold_out; _hot_out = &hot_out;
    start(input_q, hot_out, cfg);
}

void OcrUnified::processFrame(const MetadataPacket &metadata) {
    try {
        if (!_shm_ptr) { log("[ERROR] No shared memory available"); return; }

        size_t frame_size = static_cast<size_t>(metadata.width) * metadata.height * 4;
        if (frame_size > _shm_size) {
            char buf[160]; snprintf(buf, sizeof(buf), "[ERROR] Frame too large for shared memory: need=%zu have=%zu (w=%d h=%d)", frame_size, _shm_size, metadata.width, metadata.height);
            log(buf);
            return;
        }

        auto t1_tp = high_resolution_clock::now();
        int64_t t1_ns = duration_cast<nanoseconds>(t1_tp.time_since_epoch()).count();

        cv::Mat input_bgra(metadata.height, metadata.width, CV_8UC4, _shm_ptr);

        // DIAGNOSTIC: lightweight checksum only when verbose
        if (const char *v = std::getenv("OCR_VERBOSE"); v && (v[0]=='1'||v[0]=='T'||v[0]=='t'||v[0]=='Y'||v[0]=='y')) {
            uint64_t cs = 0;
            size_t n = std::min<size_t>(64, frame_size);
            for (size_t i = 0; i < n; ++i) cs = (cs * 131) + _shm_ptr[i];
            char buf[160]; snprintf(buf, sizeof(buf), "[DIAG] input_checksum=0x%016llx w=%d h=%d", (unsigned long long)cs, metadata.width, metadata.height);
            log(buf);
        }

        const cv::Mat &processed = preprocess_bgra_to_ocr(input_bgra);
        auto t2_tp = high_resolution_clock::now();
        int64_t t2_ns = duration_cast<nanoseconds>(t2_tp.time_since_epoch()).count();

        std::string ocr_text; float conf = -1.0f;
        int64_t t3_ns = 0;
        if (_tess) {
            _tess->SetImage(processed.data, processed.cols, processed.rows, 1, processed.step);
            char *txt = _tess->GetUTF8Text();
            if (txt) { 
                ocr_text = txt; 
                
                // DIAGNOSTIC: Log raw OCR output
                char ocr_buf[512];
                std::string preview = ocr_text.substr(0, 100);
                // Show hex values for first few chars to debug encoding
                std::string hex_preview;
                for (size_t i = 0; i < std::min(size_t(20), ocr_text.length()); i++) {
                    char hex[8];
                    snprintf(hex, sizeof(hex), "%02X ", (unsigned char)ocr_text[i]);
                    hex_preview += hex;
                }
                snprintf(ocr_buf, sizeof(ocr_buf), "[OCR-CORE-DIAGNOSTIC] Raw OCR text (len=%zu): '%s'", 
                         ocr_text.length(), preview.c_str());
                log(ocr_buf);
                snprintf(ocr_buf, sizeof(ocr_buf), "[OCR-CORE-DIAGNOSTIC] First 20 bytes (hex): %s", 
                         hex_preview.c_str());
                log(ocr_buf);
                
                delete[] txt; 
                conf = _tess->MeanTextConf() / 100.0f; 
            } else {
                log("[OCR-CORE-DIAGNOSTIC] GetUTF8Text returned NULL!");
            }
            auto t3_tp = high_resolution_clock::now();
            t3_ns = duration_cast<nanoseconds>(t3_tp.time_since_epoch()).count();
        } else {
            ocr_text = "TESSERACT_ERROR";
            log("[OCR-CORE-DIAGNOSTIC] ERROR: Tesseract not initialized!");
        }

        auto t4_tp = high_resolution_clock::now();
        int64_t t4_ns = duration_cast<nanoseconds>(t4_tp.time_since_epoch()).count();

        // Optional dump (throttled, atomic rename to avoid contention)
        if (const char *dump = std::getenv("OCR_DEBUG_DUMP"); dump && dump[0] == '1') {
            uint64_t n = g_dump_counter.fetch_add(1, std::memory_order_relaxed) + 1;
            int every = get_dump_every();
            if (n % every == 0) {
                if (std::filesystem::exists("C:/TANK")) {
                    safe_write_png("C:/TANK/_debug_mss_bgra.bmp", input_bgra);
                    safe_write_png("C:/TANK/_debug_processed.bmp", processed);
                    safe_write_text("C:/TANK/_debug_ocr_text.txt", ocr_text);
                }
            }
        }

        // Hot output
        if (_hot_out) {
            OcrResultPackage r{};
            strncpy(r.text, ocr_text.c_str(), sizeof(r.text)-1); r.text[sizeof(r.text)-1] = '\0';
            strncpy(r.correlation_id, metadata.correlation_id, sizeof(r.correlation_id)-1); r.correlation_id[sizeof(r.correlation_id)-1] = '\0';
            r.t0_ns = metadata.t0_mss_ns; r.t1_ns = t1_ns; r.t2_ns = t2_ns; r.t3_ns = t3_ns; r.t4_ns = t4_ns; r.confidence = conf;
            // Hot ring policy: no-drop after correlation — bounded microspin retry then CRITICAL
            for (int spins = 0; spins < 200; ++spins) {
                if (_hot_out->try_write(r)) break;
                if (spins == 199) log("[CRITICAL] Hot ring write failed after retry");
                _mm_pause(); // tiny backoff
            }
        }

        // Cold output
        if (_cold_out) {
            TelemetryBlob b{}; b.width = metadata.width; b.height = metadata.height;
            b.proc_width = processed.cols; b.proc_height = processed.rows;
            b.trading = {}; strncpy(b.trading.text, ocr_text.c_str(), sizeof(b.trading.text)-1); b.trading.text[sizeof(b.trading.text)-1]='\0';
            strncpy(b.trading.correlation_id, metadata.correlation_id, sizeof(b.trading.correlation_id)-1); b.trading.correlation_id[sizeof(b.trading.correlation_id)-1]='\0';
            b.trading.t0_ns = metadata.t0_mss_ns; b.trading.t1_ns = t1_ns; b.trading.t2_ns = t2_ns; b.trading.t3_ns = t3_ns; b.trading.t4_ns = t4_ns; b.trading.confidence = conf;
            // Copy images (bounded)
            size_t raw_bytes = static_cast<size_t>(metadata.width) * metadata.height * 4;
            b.raw_bgra.assign(_shm_ptr, _shm_ptr + raw_bytes);
            b.processed_bin.assign(processed.data, processed.data + (processed.total() * processed.elemSize()));
            _cold_out->try_write_drop_oldest(b);
        }
    } catch (const std::exception &e) {
        log("[OCR-CORE] Exception in processFrame");
    } catch (...) {
        log("[OCR-CORE] Unknown exception in processFrame");
    }
}

} // namespace ocr_core

