/*====================================================================*
 -  Copyright (C) 2001 Leptonica.  All rights reserved.
 -
 -  Redistribution and use in source and binary forms, with or without
 -  modification, are permitted provided that the following conditions
 -  are met:
 -  1. Redistributions of source code must retain the above copyright
 -     notice, this list of conditions and the following disclaimer.
 -  2. Redistributions in binary form must reproduce the above
 -     copyright notice, this list of conditions and the following
 -     disclaimer in the documentation and/or other materials
 -     provided with the distribution.
 -
 -  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 -  ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 -  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 -  A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL ANY
 -  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 -  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 -  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 -  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 -  OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 -  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 -  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *====================================================================*/

#ifndef  LEPTONICA_ALLTYPES_H
#define  LEPTONICA_ALLTYPES_H

    /* Standard */
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>

    /* General and configuration defs */
#include "endianness.h"
#include "environ.h"

    /* Generic and non-image-specific containers */
#include "array.h"
#include "bbuffer.h"
#include "hashmap.h"
#include "heap.h"
#include "list.h"
#include "ptra.h"
#include "queue.h"
#include "rbtree.h"
#include "stack.h"

    /* Imaging */
#include "arrayaccess.h"
#include "bmf.h"
#include "ccbord.h"
#include "colorfill.h"
#include "dewarp.h"
#include "gplot.h"
#include "imageio.h"
#include "jbclass.h"
#include "morph.h"
#include "pix.h"
#include "recog.h"
#include "regutils.h"
#include "stringcode.h"
#include "sudoku.h"
#include "watershed.h"


#endif /* LEPTONICA_ALLTYPES_H */
