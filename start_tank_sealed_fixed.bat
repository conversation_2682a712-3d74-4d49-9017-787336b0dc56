@echo off
REM ============================================================================
REM TESTRADE TANK SEALED Mode - Pure Trading Engine (No Telemetry)
REM 
REM Runs TESTRADE in fully isolated mode without any infrastructure dependencies.
REM Matches the TELEMETRY headless-nobcast mode configuration.
REM ============================================================================

title TESTRADE TANK SEALED Mode

echo.
echo ============================================================================
echo                    TESTRADE TANK SEALED MODE
echo                  Pure Trading Engine - No Infrastructure
echo ============================================================================
echo Timestamp: %date% %time%
echo.

REM Change to TANK directory
cd /d "C:\TANK"
echo Current directory: %CD%

REM Check if virtual environment exists
if exist "venv\Scripts\activate.bat" (
    echo [INFO] Found virtual environment at venv\Scripts\activate.bat
    echo [INFO] Activating virtual environment...
    call venv\Scripts\activate.bat
) else if exist ".venv\Scripts\activate.bat" (
    echo [INFO] Found virtual environment at .venv\Scripts\activate.bat
    echo [INFO] Activating virtual environment...
    call .venv\Scripts\activate.bat
) else (
    echo [ERROR] Virtual environment not found at venv\ or .venv\
    echo Please ensure the virtual environment is set up correctly.
    echo To create a virtual environment, run:
    echo   python -m venv venv
    echo   venv\Scripts\activate
    echo   pip install -r requirements.txt
    echo.
    pause
    exit /b 1
)

REM Verify Python is available and using venv
echo [INFO] Checking Python location and version...
where python
python --version

REM Verify we're using the venv Python by checking if websockets is available
python -c "import websockets; print('[SUCCESS] websockets module found - venv is active')" 2>nul
if errorlevel 1 (
    echo [ERROR] websockets module not found - venv not properly activated!
    echo [INFO] Trying to use venv Python directly...
    if exist ".venv\Scripts\python.exe" (
        set PYTHON_EXE=.venv\Scripts\python.exe
        echo [INFO] Using .venv\Scripts\python.exe directly
    ) else if exist "venv\Scripts\python.exe" (
        set PYTHON_EXE=venv\Scripts\python.exe
        echo [INFO] Using venv\Scripts\python.exe directly
    ) else (
        echo [ERROR] Cannot find venv Python executable
        pause
        exit /b 1
    )
) else (
    set PYTHON_EXE=python
    echo [SUCCESS] Virtual environment is properly activated
)

REM Update control.json for TANK_SEALED mode (matching headless-nobcast)
echo [INFO] Configuring for TANK_SEALED mode...
%PYTHON_EXE% -c "import json; c=json.load(open('utils/control.json')); c.update({'BABYSITTER_ENABLED':False,'ENABLE_IPC_DATA_DUMP':False,'ENABLE_EMERGENCY_GUI':False,'enable_intellisense_logging':False,'disable_broker':True,'disable_price_fetching_service':True,'development_mode':True}); json.dump(c, open('utils/control.json','w'), indent=4); print('[SUCCESS] Configuration updated')"

if errorlevel 1 (
    echo [ERROR] Failed to update configuration
    pause
    exit /b 1
)

echo.
echo ========================================
echo Starting Core Process Only
echo ========================================
echo.

REM Set environment variable for TANK_SEALED mode
set TESTRADE_MODE=TANK_SEALED

REM =============================================================================
REM TEMPORARY SSL FIX - DEVELOPMENT ONLY
REM Issue: Windows cannot verify Alpaca's SSL certificate
REM Root Cause: Missing/outdated certificate chain on Windows
REM This disables SSL verification globally - DO NOT USE IN PRODUCTION
REM 
REM Proper Fix: set SSL_CERT_FILE=C:\TANK\.venv\Lib\site-packages\certifi\cacert.pem
REM Or: pip install pip-system-certs (to use Windows certificate store)
REM =============================================================================
echo.
echo =============================================================================
echo [WARNING] SSL VERIFICATION DISABLED - DEVELOPMENT MODE ONLY
echo This is a temporary fix for Windows SSL certificate issues
echo DO NOT use this configuration in production!
echo =============================================================================
echo.
set PYTHONHTTPSVERIFY=0

REM Start Main Trading Engine (main.py only, no OCR process)
echo [INFO] Starting Main Trading Engine in TANK_SEALED mode...
echo.
echo Configuration:
echo   - TESTRADE_MODE: TANK_SEALED
echo   - BABYSITTER_ENABLED: False
echo   - ENABLE_IPC_DATA_DUMP: False
echo   - ENABLE_EMERGENCY_GUI: False
echo   - enable_intellisense_logging: False
echo.

%PYTHON_EXE% main.py

REM Store exit code
set EXIT_CODE=%errorlevel%

echo.
echo ============================================================================
if %EXIT_CODE% neq 0 (
    echo TANK SEALED MODE FAILED (Exit Code: %EXIT_CODE%)
) else (
    echo TANK SEALED MODE SHUTDOWN COMPLETE
)
echo ============================================================================
echo.

echo Press any key to exit...
pause >nul