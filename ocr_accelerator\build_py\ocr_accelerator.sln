﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{C79D0C69-D98C-3DAE-8A53-CEE0C52A0AF7}"
	ProjectSection(ProjectDependencies) = postProject
		{F083874A-CD55-3AAB-BC33-B8269AF68D09} = {F083874A-CD55-3AAB-BC33-B8269AF68D09}
		{303FB7D7-AD31-36D1-809B-E2A01E858E42} = {303FB7D7-AD31-36D1-809B-E2A01E858E42}
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E} = {A569AFD2-F3D4-3B79-AF56-BB2AB231958E}
		{7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E} = {7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E}
		{A65473DF-2BAF-3F2C-A733-62454F904819} = {A65473DF-2BAF-3F2C-A733-62454F904819}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{F083874A-CD55-3AAB-BC33-B8269AF68D09}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "accelerator_sharedmem", "accelerator_sharedmem.vcxproj", "{303FB7D7-AD31-36D1-809B-E2A01E858E42}"
	ProjectSection(ProjectDependencies) = postProject
		{F083874A-CD55-3AAB-BC33-B8269AF68D09} = {F083874A-CD55-3AAB-BC33-B8269AF68D09}
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E} = {A569AFD2-F3D4-3B79-AF56-BB2AB231958E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ocr_core", "ocr_core.vcxproj", "{A569AFD2-F3D4-3B79-AF56-BB2AB231958E}"
	ProjectSection(ProjectDependencies) = postProject
		{F083874A-CD55-3AAB-BC33-B8269AF68D09} = {F083874A-CD55-3AAB-BC33-B8269AF68D09}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ocr_core_smoke", "ocr_core_smoke.vcxproj", "{7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E}"
	ProjectSection(ProjectDependencies) = postProject
		{F083874A-CD55-3AAB-BC33-B8269AF68D09} = {F083874A-CD55-3AAB-BC33-B8269AF68D09}
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E} = {A569AFD2-F3D4-3B79-AF56-BB2AB231958E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ocr_daemon", "ocr_daemon.vcxproj", "{A65473DF-2BAF-3F2C-A733-62454F904819}"
	ProjectSection(ProjectDependencies) = postProject
		{F083874A-CD55-3AAB-BC33-B8269AF68D09} = {F083874A-CD55-3AAB-BC33-B8269AF68D09}
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E} = {A569AFD2-F3D4-3B79-AF56-BB2AB231958E}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C79D0C69-D98C-3DAE-8A53-CEE0C52A0AF7}.Debug|x64.ActiveCfg = Debug|x64
		{C79D0C69-D98C-3DAE-8A53-CEE0C52A0AF7}.Release|x64.ActiveCfg = Release|x64
		{C79D0C69-D98C-3DAE-8A53-CEE0C52A0AF7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C79D0C69-D98C-3DAE-8A53-CEE0C52A0AF7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F083874A-CD55-3AAB-BC33-B8269AF68D09}.Debug|x64.ActiveCfg = Debug|x64
		{F083874A-CD55-3AAB-BC33-B8269AF68D09}.Debug|x64.Build.0 = Debug|x64
		{F083874A-CD55-3AAB-BC33-B8269AF68D09}.Release|x64.ActiveCfg = Release|x64
		{F083874A-CD55-3AAB-BC33-B8269AF68D09}.Release|x64.Build.0 = Release|x64
		{F083874A-CD55-3AAB-BC33-B8269AF68D09}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F083874A-CD55-3AAB-BC33-B8269AF68D09}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F083874A-CD55-3AAB-BC33-B8269AF68D09}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F083874A-CD55-3AAB-BC33-B8269AF68D09}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{303FB7D7-AD31-36D1-809B-E2A01E858E42}.Debug|x64.ActiveCfg = Debug|x64
		{303FB7D7-AD31-36D1-809B-E2A01E858E42}.Debug|x64.Build.0 = Debug|x64
		{303FB7D7-AD31-36D1-809B-E2A01E858E42}.Release|x64.ActiveCfg = Release|x64
		{303FB7D7-AD31-36D1-809B-E2A01E858E42}.Release|x64.Build.0 = Release|x64
		{303FB7D7-AD31-36D1-809B-E2A01E858E42}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{303FB7D7-AD31-36D1-809B-E2A01E858E42}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{303FB7D7-AD31-36D1-809B-E2A01E858E42}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{303FB7D7-AD31-36D1-809B-E2A01E858E42}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E}.Debug|x64.ActiveCfg = Debug|x64
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E}.Debug|x64.Build.0 = Debug|x64
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E}.Release|x64.ActiveCfg = Release|x64
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E}.Release|x64.Build.0 = Release|x64
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A569AFD2-F3D4-3B79-AF56-BB2AB231958E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E}.Debug|x64.ActiveCfg = Debug|x64
		{7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E}.Debug|x64.Build.0 = Debug|x64
		{7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E}.Release|x64.ActiveCfg = Release|x64
		{7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E}.Release|x64.Build.0 = Release|x64
		{7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A65473DF-2BAF-3F2C-A733-62454F904819}.Debug|x64.ActiveCfg = Debug|x64
		{A65473DF-2BAF-3F2C-A733-62454F904819}.Debug|x64.Build.0 = Debug|x64
		{A65473DF-2BAF-3F2C-A733-62454F904819}.Release|x64.ActiveCfg = Release|x64
		{A65473DF-2BAF-3F2C-A733-62454F904819}.Release|x64.Build.0 = Release|x64
		{A65473DF-2BAF-3F2C-A733-62454F904819}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A65473DF-2BAF-3F2C-A733-62454F904819}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A65473DF-2BAF-3F2C-A733-62454F904819}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A65473DF-2BAF-3F2C-A733-62454F904819}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {86109C85-C644-3456-B469-5A5CC35CE59C}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
