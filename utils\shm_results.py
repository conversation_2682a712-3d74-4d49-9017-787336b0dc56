# Simple latest-result shared memory channel for Windows (named mapping)
# Writer: child OCR process relay
# Reader: parent trading process

import mmap
import os
import struct
import json
import time
from typing import Optional, Dict, Any

# Named mapping (Local\ scope is per-session)
SHM_NAME = os.environ.get('OCR_RESULTS_SHM_NAME', 'Local\\testrade_ocr_results')
SHM_SIZE = int(os.environ.get('OCR_RESULTS_SHM_SIZE', '65536'))  # 64 KiB
# Header: magic(8) + version(uint32) + seq(uint64) + length(uint32) = 24 bytes
HDR_FMT = '<8sIQI'
HDR_SIZE = struct.calcsize(HDR_FMT)
MAGIC = b'OCRRES\x00\x00'
VERSION = 1

class LatestResultWriter:
    def __init__(self, name: str = SHM_NAME, size: int = SHM_SIZE):
        self._size = size
        # Create or open named mapping
        self._mm = mmap.mmap(-1, size, tagname=name)
        # Initialize header if empty
        self._mm.seek(0)
        hdr = self._mm.read(HDR_SIZE)
        if len(hdr) != HDR_SIZE or hdr[:8] != MAGIC:
            self._mm.seek(0)
            self._mm.write(struct.pack(HDR_FMT, MAGIC, VERSION, 0, 0))
            self._mm.flush()
        self._seq = 0

    def publish(self, payload: Dict[str, Any]) -> None:
        # Serialize to JSON bytes (ensure ASCII-safe with ensure_ascii)
        data = json.dumps(payload, ensure_ascii=False).encode('utf-8')
        if HDR_SIZE + len(data) > self._size:
            # Truncate if oversized to avoid overflow
            data = data[: self._size - HDR_SIZE]
        # Increment local sequence and publish
        self._seq = (self._seq + 1) & 0xFFFFFFFFFFFFFFFF
        self._mm.seek(0)
        self._mm.write(struct.pack(HDR_FMT, MAGIC, VERSION, self._seq, len(data)))
        self._mm.write(data)
        self._mm.flush()

class LatestResultReader:
    def __init__(self, name: str = SHM_NAME, size: int = SHM_SIZE):
        self._size = size
        self._mm = mmap.mmap(-1, size, tagname=name)
        self._last_seq = 0

    def try_read(self) -> Optional[Dict[str, Any]]:
        self._mm.seek(0)
        magic, ver, seq, length = struct.unpack(HDR_FMT, self._mm.read(HDR_SIZE))
        if magic != MAGIC or length > (self._size - HDR_SIZE):
            return None
        if seq == self._last_seq:
            return None
        self._last_seq = seq
        raw = self._mm.read(length)
        try:
            return json.loads(raw.decode('utf-8', errors='replace'))
        except Exception:
            return None

if __name__ == '__main__':
    # Basic smoke: writer then reader in the same process
    w = LatestResultWriter()
    r = LatestResultReader()
    w.publish({'hello': 'world', 'seq': 1})
    time.sleep(0.01)
    print(r.try_read())

