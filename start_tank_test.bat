@echo on & cd /d C:\TANK
call .venv\Scripts\activate.bat
set TESTRADE_MODE=TANK_SEALED
set PATH=C:\TANK\ocr_accelerator\bin;%PATH% & set TESSDATA_PREFIX=C:\TANK\ocr_accelerator\bin\tessdata
set OCR_PIPE_DISABLED=1 & set OCR_DEBUG_DUMP=1 & set OCR_DEBUG_DUMP_EVERY=1
set TANK_TEST_CENTER=1
python -c "import json,mss; p=r'utils/control.json'; c=json.load(open(p)); mon=mss.mss().monitors[int(c.get('monitor_index',1))]; rc=c.get('ROI_COORDINATES',[0,0,617,137]); w,h=int(rc[2])-int(rc[0]),int(rc[3])-int(rc[1]); x1=mon['left']+(mon['width']-w)//2; y1=mon['top']+(mon['height']-h)//2; c['ROI_COORDINATES']=[x1,y1,x1+w,y1+h]; json.dump(c,open(p,'w'),indent=4); print('Centered ROI',c['ROI_COORDINATES'])"
python main.py
timeout /t 3 >nul & for %%F in ("C:\TANK\_debug_mss_bgra.png" "C:\TANK\_debug_processed.png") do if exist %%~F start "" %%~F
if exist "C:\TANK\_debug_ocr_text.txt" notepad "C:\TANK\_debug_ocr_text.txt"