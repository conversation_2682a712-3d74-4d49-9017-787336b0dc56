# Fix max_height/max_width issue  
with open('modules/ocr/ocr_service.py', 'r') as f:  
    content = f.read()  
  
# Find and fix the numpy array creation  
content = content.replace(  
    '            self._frame_buffer = np.ndarray(\n                (max_height, max_width, 4),',  
    '            # Use fixed max dimensions for shared memory buffer\n            max_height = 1080  # Max height\n            max_width = 1920   # Max width\n            self._frame_buffer = np.ndarray(\n                (max_height, max_width, 4),'  
)  
  
with open('modules/ocr/ocr_service.py', 'w') as f:  
    f.write(content)  
print('Fixed max_height/max_width issue')  
