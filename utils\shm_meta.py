# Shared-memory metadata header (width,height,t0_ns,correlation_id,seq)
# Windows named mapping via mmap tagname

import mmap
import os
import struct

META_NAME = os.environ.get('OCR_META_SHM_NAME', 'testrade_ocr_meta')
META_SIZE = 128  # plenty for header
# Layout: magic(8) 'OCRMETA\0', version(uint32), seq(uint64), width(int32), height(int32), t0_ns(int64), cid[64]
FMT = '<8sIQiiq64s'
MAGIC = b'OCRMETA\x00'
VERSION = 1

class LatestMetaWriter:
    def __init__(self, name: str = META_NAME, size: int = META_SIZE):
        self._mm = mmap.mmap(-1, size, tagname=name)
        self._seq = 0
        # init
        self._mm.seek(0)
        self._mm.write(struct.pack(FMT, MAGIC, VERSION, 0, 0, 0, 0, b''))
        self._mm.flush()

    def publish(self, width: int, height: int, t0_ns: int, correlation_id: str) -> None:
        self._seq = (self._seq + 1) & 0xFFFFFFFFFFFFFFFF
        cid_bytes = correlation_id.encode('utf-8')[:64]
        cid_bytes = cid_bytes + b'\x00' * (64 - len(cid_bytes))
        self._mm.seek(0)
        self._mm.write(struct.pack(FMT, MAGIC, VERSION, self._seq, int(width), int(height), int(t0_ns), cid_bytes))
        self._mm.flush()

