prefix=C:/TANK/ocr_accelerator/deps/leptonica
exec_prefix=${prefix}/bin
libdir=${prefix}/lib
includedir=${prefix}/include

Name: leptonica
Description: An open source C library for efficient image processing and image analysis operations
Version: 1.85.0
Requires.private:  zlib libpng libjpeg libtiff-4 libwebp libwebpmux
Libs: -L${libdir} -lleptonica-1.85.0
Libs.private:  -lgif
Cflags: -I${includedir} -I${includedir}/leptonica
