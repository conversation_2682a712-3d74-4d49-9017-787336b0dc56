﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7AE9E5EF-0FBD-352F-9FF8-7837BF865A8E}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ocr_core_smoke</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\TANK\ocr_accelerator\build_py\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ocr_core_smoke.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ocr_core_smoke</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\TANK\ocr_accelerator\build_py\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ocr_core_smoke.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ocr_core_smoke</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\TANK\ocr_accelerator\build_py\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">ocr_core_smoke.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">ocr_core_smoke</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\TANK\ocr_accelerator\build_py\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">ocr_core_smoke.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">ocr_core_smoke</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/TANK/ocr_accelerator/deps/opencv/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;TESS_IMPORTS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;TESS_IMPORTS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;C:\TANK\ocr_accelerator\deps\opencv\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;C:\TANK\ocr_accelerator\deps\opencv\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>Debug\ocr_core.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_gapi4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_highgui4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_ml4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_objdetect4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_photo4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_stitching4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_video4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_videoio4100.lib;C:\TANK\ocr_accelerator\deps\tesseract\lib\tesseract55.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_calib3d4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_dnn4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_features2d4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_flann4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_imgcodecs4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_imgproc4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_core4100.lib;C:\TANK\ocr_accelerator\deps\leptonica\lib\leptonica-1.85.0.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <DataExecutionPrevention></DataExecutionPrevention>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImageHasSafeExceptionHandlers></ImageHasSafeExceptionHandlers>
      <ImportLibrary>C:/TANK/ocr_accelerator/build_py/Debug/ocr_core_smoke.lib</ImportLibrary>
      <LinkErrorReporting></LinkErrorReporting>
      <ProgramDataBaseFile>C:/TANK/ocr_accelerator/build_py/Debug/ocr_core_smoke.pdb</ProgramDataBaseFile>
      <RandomizedBaseAddress></RandomizedBaseAddress>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/TANK/ocr_accelerator/deps/opencv/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TESS_IMPORTS;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TESS_IMPORTS;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;C:\TANK\ocr_accelerator\deps\opencv\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;C:\TANK\ocr_accelerator\deps\opencv\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>Release\ocr_core.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_gapi4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_highgui4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_ml4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_objdetect4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_photo4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_stitching4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_video4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_videoio4100.lib;C:\TANK\ocr_accelerator\deps\tesseract\lib\tesseract55.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_calib3d4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_dnn4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_features2d4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_flann4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_imgcodecs4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_imgproc4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_core4100.lib;C:\TANK\ocr_accelerator\deps\leptonica\lib\leptonica-1.85.0.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <DataExecutionPrevention></DataExecutionPrevention>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImageHasSafeExceptionHandlers></ImageHasSafeExceptionHandlers>
      <ImportLibrary>C:/TANK/ocr_accelerator/build_py/Release/ocr_core_smoke.lib</ImportLibrary>
      <LinkErrorReporting></LinkErrorReporting>
      <ProgramDataBaseFile>C:/TANK/ocr_accelerator/build_py/Release/ocr_core_smoke.pdb</ProgramDataBaseFile>
      <RandomizedBaseAddress></RandomizedBaseAddress>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/TANK/ocr_accelerator/deps/opencv/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TESS_IMPORTS;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TESS_IMPORTS;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;C:\TANK\ocr_accelerator\deps\opencv\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;C:\TANK\ocr_accelerator\deps\opencv\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>MinSizeRel\ocr_core.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_gapi4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_highgui4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_ml4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_objdetect4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_photo4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_stitching4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_video4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_videoio4100.lib;C:\TANK\ocr_accelerator\deps\tesseract\lib\tesseract55.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_calib3d4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_dnn4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_features2d4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_flann4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_imgcodecs4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_imgproc4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_core4100.lib;C:\TANK\ocr_accelerator\deps\leptonica\lib\leptonica-1.85.0.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <DataExecutionPrevention></DataExecutionPrevention>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImageHasSafeExceptionHandlers></ImageHasSafeExceptionHandlers>
      <ImportLibrary>C:/TANK/ocr_accelerator/build_py/MinSizeRel/ocr_core_smoke.lib</ImportLibrary>
      <LinkErrorReporting></LinkErrorReporting>
      <ProgramDataBaseFile>C:/TANK/ocr_accelerator/build_py/MinSizeRel/ocr_core_smoke.pdb</ProgramDataBaseFile>
      <RandomizedBaseAddress></RandomizedBaseAddress>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/TANK/ocr_accelerator/deps/opencv/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TESS_IMPORTS;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TESS_IMPORTS;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;C:\TANK\ocr_accelerator\deps\opencv\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\TANK\ocr_accelerator\deps\tesseract\include;C:\TANK\ocr_accelerator\deps\leptonica\include\leptonica;C:\TANK\ocr_accelerator\ocr_core;C:\TANK\ocr_accelerator\deps\opencv\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>RelWithDebInfo\ocr_core.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_gapi4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_highgui4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_ml4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_objdetect4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_photo4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_stitching4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_video4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_videoio4100.lib;C:\TANK\ocr_accelerator\deps\tesseract\lib\tesseract55.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_calib3d4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_dnn4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_features2d4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_flann4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_imgcodecs4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_imgproc4100.lib;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\opencv_core4100.lib;C:\TANK\ocr_accelerator\deps\leptonica\lib\leptonica-1.85.0.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <DataExecutionPrevention></DataExecutionPrevention>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImageHasSafeExceptionHandlers></ImageHasSafeExceptionHandlers>
      <ImportLibrary>C:/TANK/ocr_accelerator/build_py/RelWithDebInfo/ocr_core_smoke.lib</ImportLibrary>
      <LinkErrorReporting></LinkErrorReporting>
      <ProgramDataBaseFile>C:/TANK/ocr_accelerator/build_py/RelWithDebInfo/ocr_core_smoke.pdb</ProgramDataBaseFile>
      <RandomizedBaseAddress></RandomizedBaseAddress>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\TANK\ocr_accelerator\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/TANK/ocr_accelerator/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/TANK/ocr_accelerator -BC:/TANK/ocr_accelerator/build_py --check-stamp-file C:/TANK/ocr_accelerator/build_py/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPython\Support.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPython3.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\WindowsPaths.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Common.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Config.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11ConfigVersion.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11NewTools.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Targets.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeCXXCompiler.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeRCCompiler.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeSystem.cmake;C:\TANK\ocr_accelerator\deps\leptonica\lib\cmake\Leptonica\LeptonicaConfig-version.cmake;C:\TANK\ocr_accelerator\deps\leptonica\lib\cmake\leptonica\LeptonicaConfig.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVConfig-version.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVConfig.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVModules-release.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVModules.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractConfig.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractConfigVersion.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractTargets-release.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\TANK\ocr_accelerator\build_py\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/TANK/ocr_accelerator/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/TANK/ocr_accelerator -BC:/TANK/ocr_accelerator/build_py --check-stamp-file C:/TANK/ocr_accelerator/build_py/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPython\Support.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPython3.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\WindowsPaths.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Common.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Config.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11ConfigVersion.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11NewTools.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Targets.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeCXXCompiler.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeRCCompiler.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeSystem.cmake;C:\TANK\ocr_accelerator\deps\leptonica\lib\cmake\Leptonica\LeptonicaConfig-version.cmake;C:\TANK\ocr_accelerator\deps\leptonica\lib\cmake\leptonica\LeptonicaConfig.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVConfig-version.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVConfig.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVModules-release.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVModules.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractConfig.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractConfigVersion.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractTargets-release.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\TANK\ocr_accelerator\build_py\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/TANK/ocr_accelerator/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/TANK/ocr_accelerator -BC:/TANK/ocr_accelerator/build_py --check-stamp-file C:/TANK/ocr_accelerator/build_py/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPython\Support.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPython3.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\WindowsPaths.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Common.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Config.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11ConfigVersion.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11NewTools.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Targets.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeCXXCompiler.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeRCCompiler.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeSystem.cmake;C:\TANK\ocr_accelerator\deps\leptonica\lib\cmake\Leptonica\LeptonicaConfig-version.cmake;C:\TANK\ocr_accelerator\deps\leptonica\lib\cmake\leptonica\LeptonicaConfig.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVConfig-version.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVConfig.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVModules-release.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVModules.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractConfig.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractConfigVersion.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractTargets-release.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\TANK\ocr_accelerator\build_py\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/TANK/ocr_accelerator/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/TANK/ocr_accelerator -BC:/TANK/ocr_accelerator/build_py --check-stamp-file C:/TANK/ocr_accelerator/build_py/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPython\Support.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPython3.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\WindowsPaths.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Common.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Config.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11ConfigVersion.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11NewTools.cmake;C:\Python311\Lib\site-packages\pybind11\share\cmake\pybind11\pybind11Targets.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeCXXCompiler.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeRCCompiler.cmake;C:\TANK\ocr_accelerator\build_py\CMakeFiles\4.1.0\CMakeSystem.cmake;C:\TANK\ocr_accelerator\deps\leptonica\lib\cmake\Leptonica\LeptonicaConfig-version.cmake;C:\TANK\ocr_accelerator\deps\leptonica\lib\cmake\leptonica\LeptonicaConfig.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVConfig-version.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVConfig.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVModules-release.cmake;C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib\OpenCVModules.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractConfig.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractConfigVersion.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractTargets-release.cmake;C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract\TesseractTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\TANK\ocr_accelerator\build_py\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\TANK\ocr_accelerator\tests\main.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\TANK\ocr_accelerator\build_py\ZERO_CHECK.vcxproj">
      <Project>{F083874A-CD55-3AAB-BC33-B8269AF68D09}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\TANK\ocr_accelerator\build_py\ocr_core.vcxproj">
      <Project>{A569AFD2-F3D4-3B79-AF56-BB2AB231958E}</Project>
      <Name>ocr_core</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>