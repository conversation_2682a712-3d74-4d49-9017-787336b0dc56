import os, json, struct
from pathlib import Path

# Struct layout must match ocr_accelerator/ocr_core/ocr_config_bin.hpp
FMT = '<4i i f f f i f i f i i i i i i f f i f i I I'
VERSION = 1

# Map control.json keys (accept both core and ocr_* aliases)
def G(cfg, key, default):
    if key in cfg: return cfg[key]
    ok = 'ocr_' + key
    return cfg.get(ok, default)

def write_binary_config(path='C:/TANK/ocr_config.bin', control_path=None):
    if control_path is None:
        control_path = os.environ.get('TANK_CONTROL_JSON', r'C:\TANK\utils\control.json')
    cfg = json.loads(Path(control_path).read_text())

    roi = cfg.get('ROI_COORDINATES', [0,0,617,137])
    monitor_index = int(cfg.get('monitor_index', 1))

    red_boost  = float(G(cfg,'red_boost', G(cfg,'red_boost', 1.0)))
    green_boost= float(G(cfg,'green_boost', 1.0))
    blue_boost = float(G(cfg,'blue_boost', 1.0))
    adaptive_block_size = int(G(cfg,'threshold_block_size', G(cfg,'threshold_block_size', 25)))
    adaptive_C = float(G(cfg,'threshold_c', -6.0))
    upscale_factor = int(G(cfg,'upscale_factor', 3))
    unsharp_strength = float(G(cfg,'unsharp_strength', 1.7))
    force_black_text_on_white = 1 if G(cfg,'force_black_text_on_white', True) else 0

    apply_text_mask_cleaning = 1 if G(cfg,'apply_text_mask_cleaning', True) else 0
    text_mask_min_contour_area = int(G(cfg,'text_mask_min_contour_area', 5))
    text_mask_min_width = int(G(cfg,'text_mask_min_width', 2))
    text_mask_min_height = int(G(cfg,'text_mask_min_height', 1))

    enhance_small_symbols = 1 if G(cfg,'enhance_small_symbols', G(cfg,'enhance_small_symbols', True)) else 0
    symbol_max_height_base = int(cfg.get('symbol_max_height', cfg.get('ocr_symbol_max_height', 10)))
    period_comma_ratio_min = float(cfg.get('period_comma_ratio_min', cfg.get('ocr_period_comma_ratio_min', 0.2)))
    period_comma_ratio_max = float(cfg.get('period_comma_ratio_max', cfg.get('ocr_period_comma_ratio_max', 1.2)))
    period_comma_radius_base = int(cfg.get('period_comma_radius', cfg.get('ocr_period_comma_radius', 5)))
    hyphen_min_ratio = float(cfg.get('hyphen_min_ratio', cfg.get('ocr_hyphen_min_ratio', 3.0)))
    hyphen_min_height_base = int(cfg.get('hyphen_min_height', cfg.get('ocr_hyphen_min_height', 3)))

    # Pack with checksum placeholder
    values = [
        int(roi[0]), int(roi[1]), int(roi[2]), int(roi[3]),
        monitor_index,
        red_boost, green_boost, blue_boost,
        adaptive_block_size, adaptive_C,
        upscale_factor, unsharp_strength,
        force_black_text_on_white,
        apply_text_mask_cleaning,
        text_mask_min_contour_area, text_mask_min_width, text_mask_min_height,
        enhance_small_symbols, symbol_max_height_base,
        period_comma_ratio_min, period_comma_ratio_max,
        period_comma_radius_base,
        hyphen_min_ratio, hyphen_min_height_base,
        VERSION, 0
    ]

    data = struct.pack(FMT, *values)

    # Compute checksum (FNV-1a) over data with checksum field zeroed
    h = 2166136261
    for b in data:
        h ^= b
        h = (h * 16777619) & 0xFFFFFFFF
    # Write file atomically
    checksum_bytes = struct.pack('<I', h)
    out = data[:-4] + checksum_bytes
    tmp = path + '.tmp'
    with open(tmp, 'wb') as f:
        f.write(out)
    os.replace(tmp, path)
    return path

if __name__ == '__main__':
    print(write_binary_config())

