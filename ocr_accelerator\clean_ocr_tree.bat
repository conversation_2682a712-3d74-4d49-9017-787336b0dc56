@echo off
setlocal enableextensions
cd /d %~dp0

REM ============================================================
REM FUZZY CLEAN: Make the tree pristine for a fresh, reliable build
REM Keeps only:
REM   - deps/ (installed headers/libs/dlls)
REM   - bin/tessdata (traineddata)
REM Removes:
REM   - source/ (all downloaded sources + their build folders)
REM   - build/ (CMake build dir for module)
REM   - stray/duplicate .pyd and traineddata files in bin/
REM ============================================================

echo [CLEAN] Working dir: %cd%

REM 1) Remove module build directory
if exist build (
  echo [CLEAN] Removing build\ ...
  rmdir /s /q build
)

REM 2) Remove source trees (opencv / tesseract / leptonica) and their builds
if exist source (
  echo [CLEAN] Removing source\ ...
  rmdir /s /q source
)

REM 3) Clean bin/
if not exist bin mkdir bin
pushd bin >nul 2>&1

REM 3a) Ensure tessdata folder exists
if not exist tessdata mkdir tessdata

REM 3b) Move any traineddata from bin root into tessdata (belt-and-suspenders)
if exist eng.traineddata (
  echo [CLEAN] Moving eng.traineddata to tessdata\
  move /y eng.traineddata tessdata\ >nul
)
if exist osd.traineddata (
  echo [CLEAN] Moving osd.traineddata to tessdata\
  move /y osd.traineddata tessdata\ >nul
)

REM 3c) Remove any accelerator_sharedmem*.pyd to force a clean rebuild
if exist accelerator_sharedmem*.pyd (
  echo [CLEAN] Removing previous pyd(s)...
  del /f /q accelerator_sharedmem*.pyd
)

REM 3d) Remove known stray artifacts
if exist "Init(nullptr" (
  echo [CLEAN] Removing stray file: Init(nullptr
  del /f /q "Init(nullptr"
)

REM 3e) Summarize bin/
echo.
echo [CLEAN] bin\ after cleanup:
dir /b
popd >nul 2>&1

REM 4) Summarize top-level
for %%D in (deps bin) do (
  if exist %%D echo [KEEP] %%D\ present
)

echo.
echo [CLEAN] OCR tree is pristine. Ready for last-time build.
endlocal

