@echo off  
REM Test OCR Daemon with quiet settings  
  
echo Killing any existing daemon...  
taskkill /f /im ocr_daemon.exe 2>nul  
  
echo Setting environment for quiet operation...  
set OCR_VERBOSE=0  
set OCR_DEBUG_DUMP=0  
set OCR_HOT_LOG_EVERY=10  
set OCR_FRAME_SHM_SIZE=1048576  
set TANK_LOG_LEVEL=ERROR  
  
echo Building daemon...  
cd C:\TANK\ocr_accelerator  
cmake --build build_py --config Release --target ocr_daemon  
  
echo Starting daemon silently...  
start "" C:\TANK\ocr_accelerator\bin\ocr_daemon.exe >NUL 2>&1  
  
echo Daemon started. Ready for main.py  
pause 
