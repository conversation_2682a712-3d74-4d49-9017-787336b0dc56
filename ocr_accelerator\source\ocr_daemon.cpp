#include <windows.h>
#include <cstdio>
#include <cstring>
#include <string>
#include <vector>
#include <chrono>
#include <filesystem>
#include <fstream>
#include "../ocr_core/core.hpp"
#include "../ocr_core/ocr_config_bin.hpp"
#include "dxgi_capture.hpp"

using namespace ocr_core;
using namespace std::chrono;

static void log_line(const char* s){ if(s) std::printf("%s\n", s); }

struct MetaHeader {
    char magic[8]; // OCRMETA\0
    uint32_t version;
    uint64_t seq;
    int32_t width;
    int32_t height;
    int64_t t0_ns;
    char cid[64];
};

static HANDLE open_mapping(const char* name) {
    return OpenFileMappingA(FILE_MAP_ALL_ACCESS, FALSE, name);
}

static const char* env_or(const char* k, const char* defv){ if(const char* v=std::getenv(k)) return v; return defv; }
static bool env_eq(const char* k, const char* val){ if(const char* v=std::getenv(k)) return _stricmp(v,val)==0; return false; }

int main(){
    std::puts("[OCR-DAEMON] Starting daemon");

    // SHM names (env overrides; no external deps)
    std::string frame_name = env_or("OCR_FRAME_SHM_NAME", "testrade_ocr_framebuf");
    std::string meta_name  = env_or("OCR_META_SHM_NAME",  "testrade_ocr_meta");
    std::string results_name = env_or("OCR_RESULTS_SHM_NAME", "Local\\testrade_ocr_results");

    // Map frame (create-if-missing) — size from env or 4K fallback
    HANDLE hFrame = open_mapping(frame_name.c_str());
    bool created_frame = false;
    DWORD FRAME_SIZE = 0;
    if(const char* fs = std::getenv("OCR_FRAME_SHM_SIZE")) { FRAME_SIZE = (DWORD)std::strtoul(fs, nullptr, 10); }
    if(FRAME_SIZE == 0) { FRAME_SIZE = (DWORD)(3840 * 2160 * 4); }
    if(!hFrame){
        hFrame = CreateFileMappingA(INVALID_HANDLE_VALUE, NULL, PAGE_READWRITE, 0, FRAME_SIZE, frame_name.c_str());
        if(hFrame){ created_frame = true; std::puts("[OCR-DAEMON] Created frame mapping (owner)"); }
    }
    if(!hFrame){ std::puts("[OCR-DAEMON] ERROR: Frame mapping open/create failed"); return 2; }
    unsigned char* frame_ptr = (unsigned char*)MapViewOfFile(hFrame, FILE_MAP_ALL_ACCESS, 0,0,0);
    if(!frame_ptr){ std::puts("[OCR-DAEMON] ERROR: MapViewOfFile(frame) failed"); return 3; }
    std::printf("[OCR-DAEMON] Frame mapped at %p name='%s'%s\n", frame_ptr, frame_name.c_str(), created_frame?" (created)":"");

    // Map meta (create-if-missing)
    HANDLE hMeta = open_mapping(meta_name.c_str());
    bool created_meta = false;
    const DWORD META_SIZE = 128;
    if(!hMeta){
        hMeta = CreateFileMappingA(INVALID_HANDLE_VALUE, NULL, PAGE_READWRITE, 0, META_SIZE, meta_name.c_str());
        if(hMeta){ created_meta = true; std::puts("[OCR-DAEMON] Created meta mapping (owner)"); }
    }
    if(!hMeta){ std::puts("[OCR-DAEMON] ERROR: Meta mapping open/create failed"); return 4; }
    MetaHeader* meta = (MetaHeader*)MapViewOfFile(hMeta, FILE_MAP_ALL_ACCESS, 0,0,0);
    if(!meta){ std::puts("[OCR-DAEMON] ERROR: MapViewOfFile(meta) failed"); return 5; }

    // Results writer (simple latest JSON)
    HANDLE hRes = CreateFileMappingA(INVALID_HANDLE_VALUE, NULL, PAGE_READWRITE, 0, 65536, results_name.c_str());
    if(!hRes){ std::puts("[OCR-DAEMON] ERROR: CreateFileMapping(results) failed"); return 6; }
    unsigned char* res_ptr = (unsigned char*)MapViewOfFile(hRes, FILE_MAP_ALL_ACCESS, 0,0,0);

    // Load binary config (no deps). Path default C:\\TANK\\ocr_config.bin
    std::string cfg_path = env_or("OCR_CONFIG_BIN", "C:/TANK/ocr_config.bin");
    OcrConfigBin bin{}; bool cfg_ok=false;
    {
        std::ifstream in(cfg_path, std::ios::binary);
        if(in){ in.read(reinterpret_cast<char*>(&bin), sizeof(bin)); if(in.gcount()==sizeof(bin) && ocr_cfg_validate(bin, 1)){ cfg_ok=true; } }
    }
    if(!cfg_ok){ std::puts("[OCR-DAEMON] WARNING: Failed to load/validate ocr_config.bin; using defaults"); }

    // Build core config from binary
    Config cfg{};
    cfg.red_boost = cfg_ok ? bin.red_boost : 1.0;
    cfg.green_boost = cfg_ok ? bin.green_boost : 1.0;
    cfg.blue_boost = cfg_ok ? bin.blue_boost : 1.0;
    cfg.adaptive_block_size = cfg_ok ? bin.adaptive_block_size : 25;
    cfg.adaptive_C = cfg_ok ? bin.adaptive_C : -6.0;
    cfg.upscale_factor = cfg_ok ? bin.upscale_factor : 3;
    cfg.unsharp_strength = cfg_ok ? bin.unsharp_strength : 1.7f;
    cfg.force_black_text_on_white = cfg_ok ? (bin.force_black_text_on_white!=0) : true;
    cfg.apply_text_mask_cleaning = cfg_ok ? (bin.apply_text_mask_cleaning!=0) : true;
    cfg.text_mask_min_contour_area = cfg_ok ? bin.text_mask_min_contour_area : 5;
    cfg.text_mask_min_width = cfg_ok ? bin.text_mask_min_width : 2;
    cfg.text_mask_min_height = cfg_ok ? bin.text_mask_min_height : 1;
    cfg.enhance_small_symbols = cfg_ok ? (bin.enhance_small_symbols!=0) : true;
    cfg.symbol_max_height_upscaled = (cfg_ok ? bin.symbol_max_height_base : 10) * cfg.upscale_factor;
    cfg.period_comma_aspect_min = cfg_ok ? bin.period_comma_ratio_min : 0.2;
    cfg.period_comma_aspect_max = cfg_ok ? bin.period_comma_ratio_max : 1.2;
    cfg.period_comma_draw_radius_upscaled = (cfg_ok ? bin.period_comma_radius_base : 5) * cfg.upscale_factor;
    cfg.hyphen_like_min_aspect_ratio_upscaled = cfg_ok ? bin.hyphen_min_ratio : 3.0;
    cfg.hyphen_like_draw_min_height_upscaled = (cfg_ok ? bin.hyphen_min_height_base : 3) * cfg.upscale_factor;

    // Core wiring (existing worker + rings)
    OcrUnified core; core.set_logger(log_line); core.initialize_shared_memory(frame_name, (size_t)FRAME_SIZE);
    SPSCQueue<MetadataPacket,2> input_q; SPSCQueue<OcrResultPackage> hot_q;
    core.start(input_q, hot_q, cfg);

    // Capture mode: shm (default) or dxgi
    bool use_dxgi = env_eq("OCR_CAPTURE_MODE","dxgi");
    DxgiRoiCapturer dxgi;
    RECT roi_px{0,0,0,0};
    int mon_index = 1;
    if(cfg_ok){ mon_index = bin.monitor_index>0?bin.monitor_index:1; roi_px.left = bin.roi[0]; roi_px.top = bin.roi[1]; roi_px.right = bin.roi[2]; roi_px.bottom = bin.roi[3]; }
    if(use_dxgi){
        if(!dxgi.init(mon_index)) {
            std::puts("[OCR-DAEMON] DXGI init FAILED — falling back to SHM capture");
            use_dxgi=false;
        } else {
            // Clamp ROI to desktop bounds
            int mw = dxgi.monitor_width(); int mh = dxgi.monitor_height();
            if (mw>0 && mh>0) {
                roi_px.left = std::max<LONG>(0, std::min<LONG>(roi_px.left, mw-1));
                roi_px.top  = std::max<LONG>(0, std::min<LONG>(roi_px.top,  mh-1));
                roi_px.right = std::max<LONG>(roi_px.left+1, std::min<LONG>(roi_px.right, mw));
                roi_px.bottom= std::max<LONG>(roi_px.top+1,  std::min<LONG>(roi_px.bottom, mh));
            }
            char line[256]; std::snprintf(line, sizeof(line), "[OCR-DAEMON] capture_mode=dxgi monitor=%d roi=[%ld,%ld,%ld,%ld]", mon_index, roi_px.left, roi_px.top, roi_px.right, roi_px.bottom);
            std::puts(line);
        }
    }

    uint64_t last_seq = 0; uint64_t out_seq = 0;
    // Pre-size ROI buffer from configured ROI (fallback 1920x1080)
    int roi_w = (roi_px.right>roi_px.left)? (roi_px.right - roi_px.left) : 1920;
    int roi_h = (roi_px.bottom>roi_px.top)? (roi_px.bottom - roi_px.top) : 1080;
    std::vector<uint8_t> roi_buf((size_t)roi_w * roi_h * 4u);

    while(true){
        if(use_dxgi){
            // DXGI path: capture ROI directly and feed core via existing rings
            int rw=0,rh=0;
            if(dxgi.capture_roi_to_buffer(roi_px, roi_buf.data(), roi_w*4, rw, rh)){
                // Define t0 as the moment pixels are available in CPU memory (exclude frame-wait)
                auto t0_tp = std::chrono::high_resolution_clock::now();
                int64_t t0_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(t0_tp.time_since_epoch()).count();
                size_t bytes = (size_t)rw * rh * 4u;
                core.write_bytes(roi_buf.data(), bytes);
                MetadataPacket md{}; md.width = rw; md.height = rh; md.t0_mss_ns = t0_ns; md.correlation_id[0] = '\0';
                (void)input_q.try_write(md);
                core.notify_new_frame();
                // Drain results like SHM path
                OcrResultPackage p{}; while(hot_q.try_read(p)){
                    char buf[2048];
                    std::snprintf(buf, sizeof(buf),
                        "{\"type\":\"ocr_trading_signal\",\"data\":{\"full_raw_text\":\"%s\",\"overall_confidence\":%.3f,\"correlation_id\":\"%s\",\"t0_mss_ns\":%lld,\"t1_preproc_ns\":%lld,\"t2_tess_begin_ns\":%lld,\"t3_ocr_end_ns\":%lld,\"t4_total_ns\":%lld}}",
                        p.text, (double)p.confidence, p.correlation_id, (long long)p.t0_ns, (long long)p.t1_ns, (long long)p.t2_ns, (long long)p.t3_ns, (long long)p.t4_ns);
                    if(res_ptr){ out_seq++; std::memcpy(res_ptr+0,"OCRRES\x00\x00",8); uint32_t ver=1; std::memcpy(res_ptr+8,&ver,4); std::memcpy(res_ptr+12,&out_seq,8); uint32_t len=(uint32_t)std::strlen(buf); std::memcpy(res_ptr+20,&len,4); std::memcpy(res_ptr+24,buf,len); FlushViewOfFile(res_ptr,24+len);}
                }
            }
            Sleep(1);
            continue;
        }

        // SHM path: wait on metadata from Python and notify worker
        if(meta->seq != last_seq){
            last_seq = meta->seq;
            MetadataPacket md{}; md.width = meta->width; md.height = meta->height; md.t0_mss_ns = meta->t0_ns; std::strncpy(md.correlation_id, meta->cid, 63); md.correlation_id[63] = '\0';
            (void)input_q.try_write(md);
            core.notify_new_frame();
            OcrResultPackage p{}; while(hot_q.try_read(p)){
                char buf[2048];
                std::snprintf(buf, sizeof(buf),
                    "{\"type\":\"ocr_trading_signal\",\"data\":{\"full_raw_text\":\"%s\",\"overall_confidence\":%.3f,\"correlation_id\":\"%s\",\"t0_mss_ns\":%lld,\"t3_ocr_end_ns\":%lld,\"t4_total_ns\":%lld}}",
                    p.text, (double)p.confidence, p.correlation_id, (long long)p.t0_ns, (long long)p.t3_ns, (long long)p.t4_ns);
                if(res_ptr){ out_seq++; std::memcpy(res_ptr+0,"OCRRES\x00\x00",8); uint32_t ver=1; std::memcpy(res_ptr+8,&ver,4); std::memcpy(res_ptr+12,&out_seq,8); uint32_t len=(uint32_t)std::strlen(buf); std::memcpy(res_ptr+20,&len,4); std::memcpy(res_ptr+24,buf,len); FlushViewOfFile(res_ptr,24+len);}
            }
        }
        Sleep(1);
    }
}

