# CMake generation dependency list for this directory.
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCommonLanguageInclude.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindDependencyMacro.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeGenericSystem.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeInitializeConfigs.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeLanguageInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeRCInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeSystemSpecificInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeSystemSpecificInitialize.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCXXCompilerFlag.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCXXSourceCompiles.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/MSVC.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindPackageHandleStandardArgs.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindPackageMessage.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindPython/Support.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindPython3.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeCXXLinkerInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeCommonLinkerInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckCompilerFlag.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckFlagCommonConfig.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Linker/MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Linker/MSVC.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-MSVC.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-Initialize.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/WindowsPaths.cmake
C:/Python311/Lib/site-packages/pybind11/share/cmake/pybind11/pybind11Common.cmake
C:/Python311/Lib/site-packages/pybind11/share/cmake/pybind11/pybind11Config.cmake
C:/Python311/Lib/site-packages/pybind11/share/cmake/pybind11/pybind11ConfigVersion.cmake
C:/Python311/Lib/site-packages/pybind11/share/cmake/pybind11/pybind11NewTools.cmake
C:/Python311/Lib/site-packages/pybind11/share/cmake/pybind11/pybind11Targets.cmake
C:/TANK/ocr_accelerator/CMakeLists.txt
C:/TANK/ocr_accelerator/build_py/CMakeFiles/4.1.0/CMakeCXXCompiler.cmake
C:/TANK/ocr_accelerator/build_py/CMakeFiles/4.1.0/CMakeRCCompiler.cmake
C:/TANK/ocr_accelerator/build_py/CMakeFiles/4.1.0/CMakeSystem.cmake
C:/TANK/ocr_accelerator/deps/leptonica/lib/cmake/Leptonica/LeptonicaConfig-version.cmake
C:/TANK/ocr_accelerator/deps/leptonica/lib/cmake/leptonica/LeptonicaConfig.cmake
C:/TANK/ocr_accelerator/deps/opencv/x64/vc17/lib/OpenCVConfig-version.cmake
C:/TANK/ocr_accelerator/deps/opencv/x64/vc17/lib/OpenCVConfig.cmake
C:/TANK/ocr_accelerator/deps/opencv/x64/vc17/lib/OpenCVModules-release.cmake
C:/TANK/ocr_accelerator/deps/opencv/x64/vc17/lib/OpenCVModules.cmake
C:/TANK/ocr_accelerator/deps/tesseract/lib/cmake/tesseract/TesseractConfig.cmake
C:/TANK/ocr_accelerator/deps/tesseract/lib/cmake/tesseract/TesseractConfigVersion.cmake
C:/TANK/ocr_accelerator/deps/tesseract/lib/cmake/tesseract/TesseractTargets-release.cmake
C:/TANK/ocr_accelerator/deps/tesseract/lib/cmake/tesseract/TesseractTargets.cmake
