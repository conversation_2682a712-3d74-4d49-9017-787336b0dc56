﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>C:\TANK\ocr_accelerator\build_py\x64\Release\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\TANK\ocr_accelerator\bin\accelerator_sharedmem.pyd</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\TANK\ocr_accelerator\build_py\Release\ocr_core_smoke.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\TANK\ocr_accelerator\bin\ocr_daemon.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\TANK\ocr_accelerator\build_py\x64\Release\ALL_BUILD</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>