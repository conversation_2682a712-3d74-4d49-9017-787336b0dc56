#include "../ocr_core/core.hpp"
#include <opencv2/opencv.hpp>
#include <iostream>
#include <filesystem>

using namespace ocr_core;

int main(int argc, char** argv) {
    std::cout << "OCR core smoke test" << std::endl;

    // Load an image from disk (argument or fallback)
    std::string img_path = argc > 1 ? argv[1] : "C:/TANK/_debug_mss_bgra.png";
    if (!std::filesystem::exists(img_path)) {
        std::cerr << "Image not found: " << img_path << std::endl;
        return 1;
    }

    cv::Mat bgra = cv::imread(img_path, cv::IMREAD_UNCHANGED);
    if (bgra.empty()) {
        std::cerr << "Failed to read image: " << img_path << std::endl;
        return 1;
    }

    if (bgra.channels() != 4) {
        cv::cvtColor(bgra, bgra, cv::COLOR_BGR2BGRA);
    }

    // Create engine and preprocess using member function
    OcrUnified engine;
    const cv::Mat& processed = engine.preprocess_bgra_to_ocr(bgra);

    // Minimal hack: ensure grayscale 1-channel, explicit deep copy
    cv::Mat mono = (processed.channels() == 1)
        ? processed.clone()
        : [&]{ cv::Mat tmp; cv::cvtColor(processed, tmp, cv::COLOR_BGR2GRAY); return tmp; }();

    std::unique_ptr<tesseract::TessBaseAPI> tess = std::make_unique<tesseract::TessBaseAPI>();
    std::string tessdata_path = std::getenv("TESSDATA_PREFIX") ? std::getenv("TESSDATA_PREFIX") : ".\\tessdata";
    if (tess->Init(tessdata_path.c_str(), "eng") != 0) {
        std::cerr << "Tesseract init failed" << std::endl;
        return 2;
    }
    tess->SetPageSegMode(tesseract::PSM_AUTO);

    tess->SetImage(mono.data, mono.cols, mono.rows, 1, mono.step);
    char* text = tess->GetUTF8Text();
    if (text) {
        std::cout << "OCR text: " << text << std::endl;
        delete[] text;
    } else {
        std::cout << "No text" << std::endl;
    }

    std::cout << "Smoke test complete" << std::endl;
    return 0;
}

