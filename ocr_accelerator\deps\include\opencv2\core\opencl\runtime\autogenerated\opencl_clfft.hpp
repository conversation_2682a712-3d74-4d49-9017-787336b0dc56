//
// AUTOGENERATED, DO NOT EDIT
//
#ifndef OPENCV_CORE_OCL_RUNTIME_CLAMDFFT_HPP
#error "Invalid usage"
#endif

// generated by parser_clfft.py
#define clfftBakePlan clfftBakePlan_
#define clfftCopyPlan clfftCopyPlan_
#define clfftCreateDefaultPlan clfftCreateDefaultPlan_
#define clfftDestroyPlan clfftDestroyPlan_
#define clfftEnqueueTransform clfftEnqueueTransform_
#define clfftGetLayout clfftGetLayout_
#define clfftGetPlanBatchSize clfftGetPlanBatchSize_
#define clfftGetPlanContext clfftGetPlanContext_
#define clfftGetPlanDim clfftGetPlanDim_
#define clfftGetPlanDistance clfftGetPlanDistance_
#define clfftGetPlanInStride clfftGetPlanInStride_
#define clfftGetPlanLength clfftGetPlanLength_
#define clfftGetPlanOutStride clfftGetPlanOutStride_
#define clfftGetPlanPrecision clfftGetPlanPrecision_
#define clfftGetPlanScale clfftGetPlanScale_
#define clfftGetPlanTransposeResult clfftGetPlanTransposeResult_
#define clfftGetResultLocation clfftGetResultLocation_
#define clfftGetTmpBufSize clfftGetTmpBufSize_
#define clfftGetVersion clfftGetVersion_
#define clfftSetLayout clfftSetLayout_
#define clfftSetPlanBatchSize clfftSetPlanBatchSize_
#define clfftSetPlanCallback clfftSetPlanCallback_
#define clfftSetPlanDim clfftSetPlanDim_
#define clfftSetPlanDistance clfftSetPlanDistance_
#define clfftSetPlanInStride clfftSetPlanInStride_
#define clfftSetPlanLength clfftSetPlanLength_
#define clfftSetPlanOutStride clfftSetPlanOutStride_
#define clfftSetPlanPrecision clfftSetPlanPrecision_
#define clfftSetPlanScale clfftSetPlanScale_
#define clfftSetPlanTransposeResult clfftSetPlanTransposeResult_
#define clfftSetResultLocation clfftSetResultLocation_
#define clfftSetup clfftSetup_
#define clfftTeardown clfftTeardown_

#include <clFFT.h>

// generated by parser_clfft.py
#undef clfftBakePlan
#define clfftBakePlan clfftBakePlan_pfn
#undef clfftCopyPlan
//#define clfftCopyPlan clfftCopyPlan_pfn
#undef clfftCreateDefaultPlan
#define clfftCreateDefaultPlan clfftCreateDefaultPlan_pfn
#undef clfftDestroyPlan
#define clfftDestroyPlan clfftDestroyPlan_pfn
#undef clfftEnqueueTransform
#define clfftEnqueueTransform clfftEnqueueTransform_pfn
#undef clfftGetLayout
//#define clfftGetLayout clfftGetLayout_pfn
#undef clfftGetPlanBatchSize
//#define clfftGetPlanBatchSize clfftGetPlanBatchSize_pfn
#undef clfftGetPlanContext
//#define clfftGetPlanContext clfftGetPlanContext_pfn
#undef clfftGetPlanDim
//#define clfftGetPlanDim clfftGetPlanDim_pfn
#undef clfftGetPlanDistance
//#define clfftGetPlanDistance clfftGetPlanDistance_pfn
#undef clfftGetPlanInStride
//#define clfftGetPlanInStride clfftGetPlanInStride_pfn
#undef clfftGetPlanLength
//#define clfftGetPlanLength clfftGetPlanLength_pfn
#undef clfftGetPlanOutStride
//#define clfftGetPlanOutStride clfftGetPlanOutStride_pfn
#undef clfftGetPlanPrecision
//#define clfftGetPlanPrecision clfftGetPlanPrecision_pfn
#undef clfftGetPlanScale
//#define clfftGetPlanScale clfftGetPlanScale_pfn
#undef clfftGetPlanTransposeResult
//#define clfftGetPlanTransposeResult clfftGetPlanTransposeResult_pfn
#undef clfftGetResultLocation
//#define clfftGetResultLocation clfftGetResultLocation_pfn
#undef clfftGetTmpBufSize
#define clfftGetTmpBufSize clfftGetTmpBufSize_pfn
#undef clfftGetVersion
#define clfftGetVersion clfftGetVersion_pfn
#undef clfftSetLayout
#define clfftSetLayout clfftSetLayout_pfn
#undef clfftSetPlanBatchSize
#define clfftSetPlanBatchSize clfftSetPlanBatchSize_pfn
#undef clfftSetPlanCallback
//#define clfftSetPlanCallback clfftSetPlanCallback_pfn
#undef clfftSetPlanDim
//#define clfftSetPlanDim clfftSetPlanDim_pfn
#undef clfftSetPlanDistance
#define clfftSetPlanDistance clfftSetPlanDistance_pfn
#undef clfftSetPlanInStride
#define clfftSetPlanInStride clfftSetPlanInStride_pfn
#undef clfftSetPlanLength
//#define clfftSetPlanLength clfftSetPlanLength_pfn
#undef clfftSetPlanOutStride
#define clfftSetPlanOutStride clfftSetPlanOutStride_pfn
#undef clfftSetPlanPrecision
#define clfftSetPlanPrecision clfftSetPlanPrecision_pfn
#undef clfftSetPlanScale
#define clfftSetPlanScale clfftSetPlanScale_pfn
#undef clfftSetPlanTransposeResult
//#define clfftSetPlanTransposeResult clfftSetPlanTransposeResult_pfn
#undef clfftSetResultLocation
#define clfftSetResultLocation clfftSetResultLocation_pfn
#undef clfftSetup
#define clfftSetup clfftSetup_pfn
#undef clfftTeardown
#define clfftTeardown clfftTeardown_pfn

// generated by parser_clfft.py
extern CL_RUNTIME_EXPORT clfftStatus (*clfftBakePlan)(clfftPlanHandle plHandle, cl_uint numQueues, cl_command_queue* commQueueFFT, void (CL_CALLBACK* pfn_notify) (clfftPlanHandle plHandle, void* user_data), void* user_data);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftCopyPlan)(clfftPlanHandle* out_plHandle, cl_context new_context, clfftPlanHandle in_plHandle);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftCreateDefaultPlan)(clfftPlanHandle* plHandle, cl_context context, const clfftDim dim, const size_t* clLengths);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftDestroyPlan)(clfftPlanHandle* plHandle);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftEnqueueTransform)(clfftPlanHandle plHandle, clfftDirection dir, cl_uint numQueuesAndEvents, cl_command_queue* commQueues, cl_uint numWaitEvents, const cl_event* waitEvents, cl_event* outEvents, cl_mem* inputBuffers, cl_mem* outputBuffers, cl_mem tmpBuffer);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetLayout)(const clfftPlanHandle plHandle, clfftLayout* iLayout, clfftLayout* oLayout);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetPlanBatchSize)(const clfftPlanHandle plHandle, size_t* batchSize);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetPlanContext)(const clfftPlanHandle plHandle, cl_context* context);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetPlanDim)(const clfftPlanHandle plHandle, clfftDim* dim, cl_uint* size);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetPlanDistance)(const clfftPlanHandle plHandle, size_t* iDist, size_t* oDist);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetPlanInStride)(const clfftPlanHandle plHandle, const clfftDim dim, size_t* clStrides);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetPlanLength)(const clfftPlanHandle plHandle, const clfftDim dim, size_t* clLengths);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetPlanOutStride)(const clfftPlanHandle plHandle, const clfftDim dim, size_t* clStrides);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetPlanPrecision)(const clfftPlanHandle plHandle, clfftPrecision* precision);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetPlanScale)(const clfftPlanHandle plHandle, clfftDirection dir, cl_float* scale);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetPlanTransposeResult)(const clfftPlanHandle plHandle, clfftResultTransposed* transposed);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetResultLocation)(const clfftPlanHandle plHandle, clfftResultLocation* placeness);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetTmpBufSize)(const clfftPlanHandle plHandle, size_t* buffersize);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftGetVersion)(cl_uint* major, cl_uint* minor, cl_uint* patch);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetLayout)(clfftPlanHandle plHandle, clfftLayout iLayout, clfftLayout oLayout);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetPlanBatchSize)(clfftPlanHandle plHandle, size_t batchSize);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetPlanCallback)(clfftPlanHandle plHandle, const char* funcName, const char* funcString, int localMemSize, clfftCallbackType callbackType, cl_mem* userdata, int numUserdataBuffers);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetPlanDim)(clfftPlanHandle plHandle, const clfftDim dim);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetPlanDistance)(clfftPlanHandle plHandle, size_t iDist, size_t oDist);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetPlanInStride)(clfftPlanHandle plHandle, const clfftDim dim, size_t* clStrides);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetPlanLength)(clfftPlanHandle plHandle, const clfftDim dim, const size_t* clLengths);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetPlanOutStride)(clfftPlanHandle plHandle, const clfftDim dim, size_t* clStrides);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetPlanPrecision)(clfftPlanHandle plHandle, clfftPrecision precision);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetPlanScale)(clfftPlanHandle plHandle, clfftDirection dir, cl_float scale);
//extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetPlanTransposeResult)(clfftPlanHandle plHandle, clfftResultTransposed transposed);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetResultLocation)(clfftPlanHandle plHandle, clfftResultLocation placeness);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftSetup)(const clfftSetupData* setupData);
extern CL_RUNTIME_EXPORT clfftStatus (*clfftTeardown)();
