with open('modules/ocr/ocr_service.py', 'r') as f:  
    content = f.read()  
content = content.replace('np.copyto(target_view, frame_bgra)\n                    # CRITICAL FIX: write_bytes call', '# np.copyto(target_view, frame_bgra) # REMOVED - conflicts with write_bytes\n                    # CRITICAL FIX: write_bytes call')  
with open('modules/ocr/ocr_service.py', 'w') as f:  
    f.write(content)  
print('Fixed: Commented out np.copyto') 
