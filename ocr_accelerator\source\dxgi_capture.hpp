#pragma once
#ifndef NOMINMAX
#define NOMINMAX 1
#endif
#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <wrl/client.h>
#include <cstdint>

class DxgiRoiCapturer {
public:
    DxgiRoiCapturer() = default;
    ~DxgiRoiCapturer() { release(); }

    bool init(int monitor_index);
    void release();

    // Capture ROI to dest buffer (BGRA8). dest_stride bytes per row. Returns false on failure.
    bool capture_roi_to_buffer(const RECT& roi_px, uint8_t* dest, int dest_stride, int& out_w, int& out_h);

    int monitor_width() const { return mon_w_; }
    int monitor_height() const { return mon_h_; }

private:
    bool create_duplication();
    bool recreate_for_output(int adapter_ordinal, int output_ordinal);

    Microsoft::WRL::ComPtr<ID3D11Device> device_;
    Microsoft::WRL::ComPtr<ID3D11DeviceContext> context_;
    Microsoft::WRL::ComPtr<IDXGIOutputDuplication> duplication_;
    Microsoft::WRL::ComPtr<ID3D11Texture2D> staging_; // ROI-sized staging
    DXGI_OUTPUT_DESC out_desc_{};
    int mon_w_ = 0, mon_h_ = 0;
    int roi_w_cached_ = 0, roi_h_cached_ = 0;
};

