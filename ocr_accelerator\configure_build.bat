@echo off
REM Configure and build OCR accelerator with restored dependencies
REM Run this from VS x64 Native Tools Command Prompt

cd C:\TANK\ocr_accelerator

echo === Building Core Library (Smoke Test) ===
rmdir /s /q build_core 2>nul
cmake -S . -B build_core -G "Visual Studio 17 2022" -A x64 ^
  -DBUILD_PYBIND=OFF ^
  -DOpenCV_DIR="C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib" ^
  -DTesseract_DIR="C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract"

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: CMake configuration failed!
    exit /b 1
)

cmake --build build_core --config Release --target ocr_core_smoke
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed!
    exit /b 1
)

echo === Testing Core Library ===
set TESSDATA_PREFIX=C:\TANK\ocr_accelerator\bin\tessdata
build_core\Release\ocr_core_smoke.exe C:\TANK\_debug_mss_bgra.png
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Smoke test failed - may need test image
)

echo === Building Python Module (.pyd) ===
rmdir /s /q build_py 2>nul
cmake -S . -B build_py -G "Visual Studio 17 2022" -A x64 ^
  -DBUILD_PYBIND=ON ^
  -DOpenCV_DIR="C:\TANK\ocr_accelerator\deps\opencv\x64\vc17\lib" ^
  -DTesseract_DIR="C:\TANK\ocr_accelerator\deps\tesseract\lib\cmake\tesseract" ^
  -DPython3_ROOT_DIR="C:\Program Files\Python311"

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Python module configuration failed!
    exit /b 1
)

cmake --build build_py --config Release
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Python module build failed!
    exit /b 1
)

echo === Build Complete ===
echo Module location: C:\TANK\ocr_accelerator\bin\accelerator_sharedmem.pyd
echo.
echo To test in Python:
echo   cd C:\TANK
echo   python
echo   ^> import sys
echo   ^> sys.path.append(r'C:\TANK\ocr_accelerator\bin')
echo   ^> import accelerator_sharedmem
echo   ^> # Should import without errors