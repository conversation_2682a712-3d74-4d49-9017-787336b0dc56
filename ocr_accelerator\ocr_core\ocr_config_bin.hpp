#pragma once
#include <cstdint>
#include <cstddef>
#include <cstring>

#pragma pack(push, 1)
struct OcrConfigBin {
    // ROI and monitor
    int32_t roi[4];              // [x1,y1,x2,y2]
    int32_t monitor_index;       // 1-based
    // Core params
    float red_boost;
    float green_boost;
    float blue_boost;
    int32_t adaptive_block_size;
    float adaptive_C;
    int32_t upscale_factor;
    float unsharp_strength;
    int32_t force_black_text_on_white;   // bool as int
    // Cleaning
    int32_t apply_text_mask_cleaning;    // bool as int
    int32_t text_mask_min_contour_area;
    int32_t text_mask_min_width;
    int32_t text_mask_min_height;
    // Symbols
    int32_t enhance_small_symbols;       // bool as int
    int32_t symbol_max_height_base;      // in original pixels (pre-upscale baseline)
    float period_comma_ratio_min;
    float period_comma_ratio_max;
    int32_t period_comma_radius_base;    // baseline radius (pre-upscale)
    float hyphen_min_ratio;
    int32_t hyphen_min_height_base;      // baseline height (pre-upscale)
    // Footer
    uint32_t version;                    // structure version
    uint32_t checksum;                   // FNV32 over struct with checksum=0
};
#pragma pack(pop)

static inline uint32_t fnv1a32_update(uint32_t h, const void* buf, size_t n){
    const uint8_t *p = static_cast<const uint8_t*>(buf);
    for(size_t i=0;i<n;++i){ h ^= p[i]; h *= 16777619u; }
    return h;
}

static inline uint32_t ocr_cfg_checksum(const OcrConfigBin &cfg){
    OcrConfigBin tmp; std::memcpy(&tmp, &cfg, sizeof(tmp));
    tmp.checksum = 0u;
    uint32_t h = 2166136261u; // FNV-1a offset basis
    h = fnv1a32_update(h, &tmp, sizeof(tmp));
    return h;
}

static inline bool ocr_cfg_validate(const OcrConfigBin &cfg, uint32_t expected_version){
    if(cfg.version != expected_version) return false;
    return ocr_cfg_checksum(cfg) == cfg.checksum;
}

