@echo off
setlocal enableextensions
cd /d %~dp0

echo ==============================================
echo CLEANING PREVIOUS OCR ACCELERATOR BUILD OUTPUT
echo ==============================================

REM 1) Remove build folder (CMake intermediates)
if exist build (
  echo Removing build\ ...
  rmdir /s /q build
)

REM 2) Ensure bin exists
if not exist bin mkdir bin
cd bin

REM 3) Remove any previous pyds (ABI-suffixed and unsuffixed)
echo Removing old .pyd files...
del /f /q accelerator_sharedmem*.pyd 2>nul

REM 4) Remove any stray artifacts
if exist "Init(nullptr" (
  echo Removing stray file: Init(nullptr
  del /f /q "Init(nullptr"
)

REM 5) Ensure a single tessdata location; remove duplicates in bin root
if exist eng.traineddata (
  echo Removing duplicate eng.traineddata from bin root
  del /f /q eng.traineddata
)
if exist osd.traineddata (
  echo Removing duplicate osd.traineddata from bin root
  del /f /q osd.traineddata
)

REM 6) Ensure tessdata folder exists
if not exist tessdata mkdir tessdata

REM 7) Summarize bin contents
echo.
echo Bin directory after cleanup:
dir /b

echo.
echo Cleanup complete. Ready for a fresh build.
endlocal

