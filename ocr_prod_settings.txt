  
REM OCR Performance Settings - Production Ready  
set PATH=C:\TANK\ocr_accelerator\bin;%C:\Python311\Scripts\;C:\Python311\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;C:\Program Files\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin% & set TESSDATA_PREFIX=C:\TANK\ocr_accelerator\bin\tessdata  
set OCR_PIPE_DISABLED=1  
set OCR_DEBUG_DUMP=0  
set OCR_DEBUG_DUMP_EVERY=0  
set TANK_TEST_CENTER=0  
 
