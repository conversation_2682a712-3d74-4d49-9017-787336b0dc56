#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>
#include <cstring>
#include <memory>
#include "../ocr_core/core.hpp"

namespace py = pybind11;
using namespace ocr_core;

static ocr_core::Config config_from_dict(const py::dict &d) {
    ocr_core::Config c;
    auto getd = [&](const char *k, auto &ref){ if (d.contains(k)) ref = py::cast<std::decay_t<decltype(ref)>>(d[k]); };
    // Channel boosts
    getd("blue_boost", c.blue_boost);
    getd("green_boost", c.green_boost);
    getd("red_boost", c.red_boost);
    // Legacy/optional image ops
    getd("clahe_clip_limit", c.clahe_clip_limit);
    getd("clahe_tile_size", c.clahe_tile_size);
    getd("morph_kernel_size", c.morph_kernel_size);
    getd("bilateral_d", c.bilateral_d);
    getd("bilateral_sigma_color", c.bilateral_sigma_color);
    getd("bilateral_sigma_space", c.bilateral_sigma_space);
    getd("denoise_h", c.denoise_h);
    // Thresholding
    getd("adaptive_block_size", c.adaptive_block_size);
    getd("adaptive_C", c.adaptive_C);
    // Tuned extras (winning harness)
    getd("upscale_factor", c.upscale_factor);
    getd("unsharp_strength", c.unsharp_strength);
    getd("force_black_text_on_white", c.force_black_text_on_white);
    // Cleaning
    getd("apply_text_mask_cleaning", c.apply_text_mask_cleaning);
    getd("text_mask_min_contour_area", c.text_mask_min_contour_area);
    getd("text_mask_min_width", c.text_mask_min_width);
    getd("text_mask_min_height", c.text_mask_min_height);
    // Symbol enhancement suite
    getd("enhance_small_symbols", c.enhance_small_symbols);
    getd("symbol_max_height_upscaled", c.symbol_max_height_upscaled);
    getd("period_comma_aspect_min", c.period_comma_aspect_min);
    getd("period_comma_aspect_max", c.period_comma_aspect_max);
    getd("period_comma_draw_radius_upscaled", c.period_comma_draw_radius_upscaled);
    getd("hyphen_like_min_aspect_ratio_upscaled", c.hyphen_like_min_aspect_ratio_upscaled);
    getd("hyphen_like_draw_min_height_upscaled", c.hyphen_like_draw_min_height_upscaled);
    return c;
}

PYBIND11_MODULE(accelerator_sharedmem, m) {
    m.doc() = "OCR Accelerator (core+shim)";

    using MetaQ2 = SPSCQueue<MetadataPacket,2>;
    py::class_<MetaQ2>(m, "MetadataQueue")
        .def(py::init([](size_t){ return std::unique_ptr<MetaQ2>(new MetaQ2()); }), "Fixed capacity=2")
        .def("try_write", [](MetaQ2& q, int w, int h, int64_t t0_ns, const std::string &cid){
            MetadataPacket p{}; p.width=w; p.height=h; p.t0_mss_ns=t0_ns; strncpy(p.correlation_id,cid.c_str(),63); p.correlation_id[63]='\0'; return q.try_write_drop_oldest(p);
        }, py::arg("width"), py::arg("height"), py::arg("t0_ns"), py::arg("correlation_id"))
        .def("try_read", [](MetaQ2& q) -> py::object { MetadataPacket p{}; if(q.try_read(p)){ py::dict r; r["width"]=p.width; r["height"]=p.height; r["t0_ns"]=p.t0_mss_ns; r["correlation_id"]=std::string(p.correlation_id); return r; } return py::none(); })
        .def("size", [](const MetaQ2& q){ return q.size(); })
        .def("capacity", [](const MetaQ2& q){ return q.capacity(); })
        .def("dropped_oldest_count", [](const MetaQ2& q){ return q.dropped_oldest_count(); });

    py::class_<SPSCQueue<OcrResultPackage>>(m, "ResultRingBuffer")
        .def(py::init([](size_t){ return std::unique_ptr<SPSCQueue<OcrResultPackage>>(new SPSCQueue<OcrResultPackage>()); }), "Capacity hint (ignored; fixed at compile-time)")
        .def("try_read", [](SPSCQueue<OcrResultPackage>& q) -> py::object { OcrResultPackage p{}; if(q.try_read(p)){ return py::cast(p); } return py::none(); });

    // Telemetry ring: returns a Python dict with bytes to avoid heavy object exposure
    using TeleQ = SPSCQueue<TelemetryBlob, 1024>;
    py::class_<TeleQ>(m, "TelemetryRingBuffer")
        .def(py::init([](size_t){ return std::unique_ptr<TeleQ>(new TeleQ()); }), "Fixed capacity~1000")
        .def("try_read", [](TeleQ& q) -> py::object {
            TelemetryBlob b{}; if(!q.try_read(b)) return py::none();
            py::dict d;
            d["width"] = b.width; d["height"] = b.height; d["proc_width"] = b.proc_width; d["proc_height"] = b.proc_height;
            d["text"] = std::string(b.trading.text);
            d["correlation_id"] = std::string(b.trading.correlation_id);
            d["t0_ns"] = b.trading.t0_ns; d["t1_ns"] = b.trading.t1_ns; d["t2_ns"] = b.trading.t2_ns; d["t3_ns"] = b.trading.t3_ns; d["t4_ns"] = b.trading.t4_ns;
            d["confidence"] = b.trading.confidence;
            d["raw_bgra"] = py::bytes(reinterpret_cast<const char*>(b.raw_bgra.data()), b.raw_bgra.size());
            d["processed_bin"] = py::bytes(reinterpret_cast<const char*>(b.processed_bin.data()), b.processed_bin.size());
            return d;
        });

    py::class_<OcrResultPackage>(m, "OcrResultPackage")
        .def_readonly("text", &OcrResultPackage::text)
        .def_property_readonly("text_bytes", [](const OcrResultPackage &p){ return py::bytes(p.text, std::strlen(p.text)); })
        .def_readonly("correlation_id", &OcrResultPackage::correlation_id)
        .def_readonly("t0_ns", &OcrResultPackage::t0_ns)
        .def_readonly("t1_ns", &OcrResultPackage::t1_ns)
        .def_readonly("t2_ns", &OcrResultPackage::t2_ns)
        .def_readonly("t3_ns", &OcrResultPackage::t3_ns)
        .def_readonly("t4_ns", &OcrResultPackage::t4_ns)
        .def_readonly("confidence", &OcrResultPackage::confidence);

    py::class_<OcrUnified>(m, "OcrUnified")
        .def(py::init<>())
        .def("initialize_shared_memory", &OcrUnified::initialize_shared_memory, py::call_guard<py::gil_scoped_release>())
        .def("write_bytes", [](OcrUnified &o, py::buffer b){
            auto info = b.request();
            // Small copy; keep GIL or release after grabbing pointer
            o.write_bytes(info.ptr, static_cast<size_t>(info.size * info.itemsize));
        }, py::arg("buffer"))
        .def("start", [](OcrUnified &o, SPSCQueue<MetadataPacket,2>& in, SPSCQueue<OcrResultPackage>& hot, const py::dict &params){
            // Convert under GIL, then release for the long-running start
            Config c = config_from_dict(params);
            py::gil_scoped_release rel;
            o.start(in, hot, c);
        })
        .def("start", [](OcrUnified &o, SPSCQueue<MetadataPacket,2>& in, SPSCQueue<OcrResultPackage>& hot, SPSCQueue<TelemetryBlob,1024>& cold, const py::dict &params){
            Config c = config_from_dict(params);
            py::gil_scoped_release rel;
            o.start(in, hot, cold, c);
        })
        .def("notify_new_frame", &OcrUnified::notify_new_frame)
        .def("stop", &OcrUnified::stop, py::call_guard<py::gil_scoped_release>());
}

