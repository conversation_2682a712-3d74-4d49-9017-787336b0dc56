PERFORMANCE_TRACKER_REAL.PY LOADED. Initial PERFORMANCE_TRACKING_ENABLED = False
Setting performance tracking to True. Previous state: False
WARNING:utils.global_config:Config key 'FEATURE_FLAG_USE_DIRECT_OCR_CONDITIONED_TO_ORCHESTRATOR' not found in 'utils/control.json'. Using default value: True
WARNING:utils.global_config:Config key 'USE_DIRECT_IPC_TELEMETRY' not found in 'utils/control.json'. Using default value: True
WARNING:utils.global_config:Config key 'redis_stream_signal_decisions' not found in 'utils/control.json'. Using default value: testrade:signal-decisions
WARNING:utils.global_config:Config key 'redis_stream_execution_decisions' not found in 'utils/control.json'. Using default value: testrade:execution-decisions
WARNING:utils.global_config:Config key 'redis_stream_unified_timeline' not found in 'utils/control.json'. Using default value: testrade:unified-timeline
2025-08-21 02:22:17 - MainThread      - root                           - INFO     - --- Logging system initialized ---
2025-08-21 02:22:17 - MainThread      - root                           - INFO     - Log Level: INFO
2025-08-21 02:22:17 - MainThread      - root                           - INFO     - File logging disabled for long test run
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - Starting centralized service registration...
2025-08-21 02:22:17 - MainThread      - utils.ipc_endpoints            - INFO     - IPC endpoints configured: {'platform': 'Windows', 'is_wsl': False, 'ipc_base_path': 'N/A (Windows)'}
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - DI_LOCKDOWN: Registered AegisCorrelationLogger with platform-aware TelemetryClient.
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - AEGIS PRIME: IBulletproofBabysitterIPCClient NOT registered - lives in TelemetryService process only
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - DI Registration: Phase 3 cleanup complete. All obsolete telemetry interfaces removed.
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - DI_REGISTRATION: IBulkDataService has been registered.
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - Registered IOrderStateService - core order state management
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - Registered IOrderLinkingService - ID mapping management
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - Registered IOrderEventProcessor - broker event handling
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - Registered IOrderQueryService - read-only order queries
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - DI_REGISTRATION: OCRServiceFactory has been registered.
2025-08-21 02:22:17 - MainThread      - core.di_registration           - CRITICAL - DI_SHIM: Registered ProcessOrchestrator under old IOCRProcessManager interface for backward compatibility.
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - === AEGIS PRIME HYBRID REGISTRATION COMPLETE ===
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - Status: NEW telemetry + LEGACY services
2025-08-21 02:22:17 - MainThread      - core.di_registration           - INFO     - Centralized service registration completed successfully
2025-08-21 02:22:17 - MainThread      - root                           - INFO     - MAIN: Performing prerequisite broker handshake...
2025-08-21 02:22:18 - MainThread      - core.di_registration           - INFO     - DI Registration: Alpaca REST client created for JIT Price Oracle.
2025-08-21 02:22:18 - MainThread      - modules.price_fetching.price_repository - INFO     - JIT Price Oracle (PriceRepository) initialized.
2025-08-21 02:22:18 - MainThread      - core.config_service            - INFO     - ConfigService initialized successfully
2025-08-21 02:22:18 - MainThread      - core.di_registration           - INFO     - DI_LOCKDOWN: Windows platform detected. Creating SmartTelemetryClient for IPC.
2025-08-21 02:22:18 - MainThread      - core.clients.smart_telemetry_client - INFO     - SmartTelemetryClient: Using TCP mode (reason: Windows platform doesn't support IPC)
2025-08-21 02:22:18 - MainThread      - core.clients.telemetry_client  - INFO     - TelemetryClient connected to tcp://127.0.0.1:7777
2025-08-21 02:22:18 - MainThread      - core.clients.telemetry_client  - INFO     - TelemetryClient connected to tcp://127.0.0.1:7778
2025-08-21 02:22:18 - MainThread      - core.clients.telemetry_client  - INFO     - TelemetryClient connected to tcp://127.0.0.1:7779
2025-08-21 02:22:18 - MainThread      - core.clients.telemetry_client  - INFO     - TelemetryClient initialized with multi-lane architecture
2025-08-21 02:22:18 - MainThread      - core.logging.aegis_correlation_logger - INFO     - AegisCorrelationLogger initialized with TelemetryClient
2025-08-21 02:22:18 - MainThread      - modules.order_management.order_repository - INFO     - Initializing OrderRepository with EventBus integration...
2025-08-21 02:22:18 - MainThread      - modules.order_management.order_repository - INFO     - EventBus provided to OrderRepository
2025-08-21 02:22:18 - MainThread      - modules.order_management.order_repository - INFO     - Broker service provided to OrderRepository
2025-08-21 02:22:18 - MainThread      - modules.order_management.order_repository - INFO     - Position manager not provided to OrderRepository - will be set later
2025-08-21 02:22:18 - MainThread      - modules.order_management.order_repository - INFO     - ICorrelationLogger provided to OrderRepository for event publishing
2025-08-21 02:22:18 - MainThread      - modules.order_management.order_repository - INFO     - Initialized _calculated_net_position cache (fill-driven).
2025-08-21 02:22:18 - MainThread      - modules.order_management.order_repository - INFO     - Initial position sync flag set to False.
2025-08-21 02:22:18 - MainThread      - modules.order_management.order_repository - INFO     - OrderRepository initialized with EventBus integration. Thread creation deferred to start() method.
2025-08-21 02:22:18 - MainThread      - broker_bridge_logger           - INFO     - LightspeedBroker initialized. Connection will be established on start().
2025-08-21 02:22:18 - MainThread      - broker_bridge_logger           - CRITICAL - PHASE 0 HANDSHAKE: Starting complete broker initialization...
2025-08-21 02:22:18 - MainThread      - broker_bridge_logger           - INFO     - PHASE 0: TANK_MODE activated - IPC data dumping disabled.
2025-08-21 02:22:18 - MainThread      - broker_bridge_logger           - CRITICAL - PHASE 0: Creating listener thread...
2025-08-21 02:22:18 - LightspeedListenThread - broker_bridge_logger           - CRITICAL - LSB_LISTEN_THREAD: Loop started.
2025-08-21 02:22:18 - LightspeedListenThread - broker_bridge_logger           - INFO     - Entering Lightspeed listen loop.
2025-08-21 02:22:18 - LightspeedListenThread - broker_bridge_logger           - CRITICAL - LSB_LISTEN_THREAD: Waiting for socket ready signal...
2025-08-21 02:22:18 - MainThread      - broker_bridge_logger           - CRITICAL - PHASE 0: Establishing socket connection...
2025-08-21 02:22:20 - MainThread      - broker_bridge_logger           - CRITICAL - PHASE 0 HANDSHAKE: FATAL FAILURE - [WinError 10061] No connection could be made because the target machine actively refused it
Traceback (most recent call last):
  File "C:\TANK\modules\broker_bridge\lightspeed_broker.py", line 350, in establish_connection
    self.client.connect()
  File "C:\TANK\utils\decorators\dummy_log_decorators.py", line 28, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\TANK\modules\broker_bridge\lightspeed_broker.py", line 118, in connect
    s.connect((self.host, self.port))
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it
2025-08-21 02:22:22 - MainThread      - root                           - CRITICAL - FATAL: Could not establish connection with broker. Application will not start.
DEBUG_CONFIG_LOAD: ocr_capture_interval_seconds = 0.005
2025-08-21 02:22:22 - MainThread      - network_diagnostics            - INFO     - Network diagnostics cleanup started
2025-08-21 02:22:22 - MainThread      - network_diagnostics            - INFO     - Network diagnostics cleanup completed
