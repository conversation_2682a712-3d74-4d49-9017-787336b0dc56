#include "dxgi_capture.hpp"
#include <algorithm>

using Microsoft::WRL::ComPtr;

bool DxgiRoiCapturer::init(int monitor_index){
    release();
    UINT flags = D3D11_CREATE_DEVICE_BGRA_SUPPORT;
#ifdef _DEBUG
    // flags |= D3D11_CREATE_DEVICE_DEBUG;
#endif
    D3D_FEATURE_LEVEL fls[] = { D3D_FEATURE_LEVEL_11_1, D3D_FEATURE_LEVEL_11_0 };
    D3D_FEATURE_LEVEL got;
    HRESULT hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, flags, fls, 2, D3D11_SDK_VERSION, &device_, &got, &context_);
    if(FAILED(hr)) return false;

    ComPtr<IDXGIDevice> dxgiDevice; device_.As(&dxgiDevice);
    ComPtr<IDXGIAdapter> adapter; dxgiDevice->GetAdapter(&adapter);
    ComPtr<IDXGIOutput> output;
    if(FAILED(adapter->EnumOutputs(std::max(0,monitor_index-1), &output))) return false;
    output->GetDesc(&out_desc_);
    DXGI_OUTDUPL_DESC dd{};
    return recreate_for_output(0, std::max(0,monitor_index-1));
}

void DxgiRoiCapturer::release(){
    staging_.Reset(); duplication_.Reset(); context_.Reset(); device_.Reset();
    mon_w_ = mon_h_ = 0; roi_w_cached_ = roi_h_cached_ = 0;
}

bool DxgiRoiCapturer::recreate_for_output(int adapter_ordinal, int output_ordinal){
    ComPtr<IDXGIDevice> dxgiDevice; if(FAILED(device_.As(&dxgiDevice))) return false;
    ComPtr<IDXGIAdapter> adapter; if(FAILED(dxgiDevice->GetAdapter(&adapter))) return false;
    ComPtr<IDXGIOutput> output; if(FAILED(adapter->EnumOutputs(output_ordinal, &output))) return false;
    ComPtr<IDXGIOutput1> out1; if(FAILED(output.As(&out1))) return false;

    DXGI_OUTPUT_DESC od{}; output->GetDesc(&od);
    out_desc_ = od;

    ComPtr<IDXGIOutputDuplication> dup; if(FAILED(out1->DuplicateOutput(device_.Get(), &dup))) return false;
    duplication_ = dup;

    // Query desktop size from duplication desc
    DXGI_OUTDUPL_DESC ddesc{}; duplication_->GetDesc(&ddesc);
    mon_w_ = ddesc.ModeDesc.Width; mon_h_ = ddesc.ModeDesc.Height;

    return true;
}

bool DxgiRoiCapturer::create_duplication(){
    return true;
}

bool DxgiRoiCapturer::capture_roi_to_buffer(const RECT& roi_px, uint8_t* dest, int dest_stride, int& out_w, int& out_h){
    if(!duplication_) return false;
    DXGI_OUTDUPL_FRAME_INFO fi{}; ComPtr<IDXGIResource> res;
    HRESULT hr = duplication_->AcquireNextFrame(16, &fi, &res);
    if(hr == DXGI_ERROR_WAIT_TIMEOUT) return false; // no new frame
    if(FAILED(hr)) { duplication_.Reset(); return false; }

    ComPtr<ID3D11Texture2D> src; res.As(&src);
    D3D11_TEXTURE2D_DESC sdesc{}; src->GetDesc(&sdesc);

    int rw = std::clamp((int)(roi_px.right - roi_px.left), 1, (int)sdesc.Width);
    int rh = std::clamp((int)(roi_px.bottom - roi_px.top), 1, (int)sdesc.Height);

    if(!staging_ || roi_w_cached_!=rw || roi_h_cached_!=rh){
        roi_w_cached_ = rw; roi_h_cached_ = rh;
        D3D11_TEXTURE2D_DESC td{}; td.Width=rw; td.Height=rh; td.MipLevels=1; td.ArraySize=1; td.Format=DXGI_FORMAT_B8G8R8A8_UNORM; td.SampleDesc.Count=1; td.Usage=D3D11_USAGE_STAGING; td.CPUAccessFlags=D3D11_CPU_ACCESS_READ; td.BindFlags=0;
        ComPtr<ID3D11Texture2D> st; if(FAILED(device_->CreateTexture2D(&td, nullptr, &st))) { duplication_->ReleaseFrame(); return false; }
        staging_ = st;
    }

    D3D11_BOX box{}; box.left=roi_px.left; box.top=roi_px.top; box.front=0; box.right=roi_px.right; box.bottom=roi_px.bottom; box.back=1;
    context_->CopySubresourceRegion(staging_.Get(), 0, 0, 0, 0, src.Get(), 0, &box);

    D3D11_MAPPED_SUBRESOURCE map{}; if(FAILED(context_->Map(staging_.Get(), 0, D3D11_MAP_READ, 0, &map))) { duplication_->ReleaseFrame(); return false; }
    out_w = rw; out_h = rh;
    const uint8_t* srcp = (const uint8_t*)map.pData; int src_pitch = (int)map.RowPitch;
    for(int y=0;y<rh;++y){ memcpy(dest + y*dest_stride, srcp + y*src_pitch, rw*4); }
    context_->Unmap(staging_.Get(), 0);

    duplication_->ReleaseFrame();
    return true;
}

